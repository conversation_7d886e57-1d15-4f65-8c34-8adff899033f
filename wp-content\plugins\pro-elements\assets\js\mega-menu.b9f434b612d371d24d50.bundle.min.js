/*! elementor-pro - v3.30.0 - 01-07-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[727],{3556:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=class AnchorLinks{observer=null;constructor(e,t){this.$anchorLinks=e,this.activeAnchorClass=t.activeAnchorItem,this.anchorClass=t.anchorItem}getViewportHeight(){return window.innerHeight}bindEvents(){this.onResize=this.onResize.bind(this),window.addEventListener("resize",this.onResize)}initialize(){this.viewPortHeight=this.getViewportHeight(),this.followMenuAnchors(),this.bindEvents()}followMenuAnchors(){this.$anchorLinks.each(((e,t)=>{location.pathname===t.pathname&&""!==t.hash&&this.followMenuAnchor(jQuery(t))}))}followMenuAnchor(e){const t=e.hasClass(this.anchorClass)?e:e.closest(`.${this.anchorClass}`),n=this.getAnchorElement(e);if(!n)return;const i=this.getObserverOptions(n);this.observer=this.createObserver(t,e,i),this.observer.observe(n)}getAnchorElement(e){const t=e[0].hash;try{const e=decodeURIComponent(t);return document.querySelector(e)}catch(e){return null}}getObserverOptions(e){return{root:null,rootMargin:this.calculateRootMargin(e)}}calculateRootMargin(e){const t=(e?.offsetHeight||0)>this.viewPortHeight/2,n=-1*this.viewPortHeight/2;return`${t?n:0}px 0px ${n}px 0px`}createObserver(e,t,n){return new IntersectionObserver((n=>{n.forEach((n=>{e.toggleClass(this.activeAnchorClass,n.isIntersecting),t.attr("aria-current",n.isIntersecting?"location":"")}))}),n)}onResize(){this.viewPortHeight=this.getViewportHeight(),this.observer&&this.observer.disconnect(),this.followMenuAnchors()}}},6974:(e,t)=>{function getChildrenWidth(e){let t=0;const n=e[0].parentNode,i=getComputedStyle(n),o=parseFloat(i.gap)||0;for(let n=0;n<e.length;n++)t+=e[n].offsetWidth+o;return t}Object.defineProperty(t,"__esModule",{value:!0}),t.changeScrollStatus=function changeScrollStatus(e,t){"mousedown"===t.type?(e.classList.add("e-scroll"),e.dataset.pageX=t.pageX):(e.classList.remove("e-scroll","e-scroll-active"),e.dataset.pageX="")},t.setHorizontalScrollAlignment=function setHorizontalScrollAlignment(e){let{element:t,direction:n,justifyCSSVariable:i,horizontalScrollStatus:o}=e;if(!t)return;!function isHorizontalScroll(e,t){return e.clientWidth<getChildrenWidth(e.children)&&"enable"===t}(t,o)?t.style.setProperty(i,""):function initialScrollPosition(e,t,n){const i=elementorFrontend.config.is_rtl;if("end"===t)e.style.setProperty(n,"start"),e.scrollLeft=i?-1*getChildrenWidth(e.children):getChildrenWidth(e.children);else e.style.setProperty(n,"start"),e.scrollLeft=0}(t,n,i)},t.setHorizontalTitleScrollValues=function setHorizontalTitleScrollValues(e,t,n){const i=e.classList.contains("e-scroll"),o="enable"===t,s=e.scrollWidth>e.clientWidth;if(!i||!o||!s)return;n.preventDefault();const r=parseFloat(e.dataset.pageX),a=n.pageX-r;let l=0;l=20<a?5:-20>a?-5:a;e.scrollLeft=e.scrollLeft-l,e.classList.add("e-scroll-active")}},3431:(e,t,n)=>{var i=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(166),s=i(n(3556)),r=n(6974);class MegaMenu extends elementorModules.frontend.handlers.Base{constructor(){super(...arguments),elementorFrontend.isEditMode()&&(this.lifecycleChangeListener=null),this.resizeListener=null,this.prevMouseY=null,this.isKeyboardNavigation=!1}getDefaultSettings(){return{selectors:{elementorWidgetWrapper:".elementor-widget-n-menu",widgetContainer:".e-n-menu",dropdownMenuToggle:".e-n-menu-toggle",menuWrapper:".e-n-menu-wrapper",headingContainer:".e-n-menu-heading",menuItem:".e-n-menu-item",tabTitle:".e-n-menu-title",tabTitleText:".e-n-menu-title-text",directTabTitle:":scope > .elementor-widget-container > .e-n-menu > .e-n-menu-wrapper > .e-n-menu-heading > .e-n-menu-item > .e-n-menu-title, :scope > .e-n-menu > .e-n-menu-wrapper > .e-n-menu-heading > .e-n-menu-item > .e-n-menu-title",tabClickableTitle:".e-n-menu-title.e-click",tabDropdown:".e-n-menu-dropdown-icon",menuContent:".e-n-menu-content",tabContent:".e-n-menu-content > .e-con, .e-n-menu-heading > .e-con",directTabContent:":scope > .elementor-widget-container > .e-n-menu > .e-n-menu-wrapper > .e-n-menu-heading > .e-n-menu-item > .e-n-menu-content > .e-con, :scope > .elementor-widget-container > .e-n-menu > .e-n-menu-wrapper > .e-n-menu-heading > .e-con, :scope > .e-n-menu > .e-n-menu-wrapper > .e-n-menu-heading > .e-n-menu-item > .e-n-menu-content > .e-con, :scope > .e-n-menu > .e-n-menu-wrapper > .e-n-menu-heading > .e-con",tabContentBeforeInterlacing:"> .elementor-widget-container > .e-n-menu > .e-n-menu-wrapper > .e-n-menu-heading > .e-con, > .e-n-menu > .e-n-menu-wrapper > .e-n-menu-heading > .e-con",newContainerAfterRepeaterAction:"> .elementor-widget-container > .e-n-menu > .e-n-menu-wrapper > .e-n-menu-heading > .e-con, > .elementor-widget-container > .e-n-menu > .e-n-menu-wrapper > .e-n-menu-heading > .e-n-menu-item > .e-n-menu-content > .e-con:nth-child(2), > .e-n-menu > .e-n-menu-wrapper > .e-n-menu-heading > .e-con, > .e-n-menu > .e-n-menu-wrapper > .e-n-menu-heading > .e-n-menu-item > .e-n-menu-content > .e-con:nth-child(2)",anchorLink:".e-anchor a"},classes:{active:"e-active",anchorItem:"e-anchor",activeAnchorItem:"e-current"},dataAttributes:{tabIndex:"data-tab-index"},ariaAttributes:{titleStateAttribute:"aria-expanded",activeTitleSelector:'[aria-expanded="true"]'},autoExpand:!1,autoFocus:!1,showTabFn:"show",hideTabFn:"hide",toggleSelf:!1,hidePrevious:!0,postUrl:"post-url",internalUrl:"internal-url"}}getDefaultElements(){const e=this.getSettings("selectors");return{$tabContents:this.findElement(e.tabContent),$widgetContainer:this.findElement(e.widgetContainer),$dropdownMenuToggle:this.findElement(e.dropdownMenuToggle),$menuWrapper:this.findElement(e.menuWrapper),$menuContent:this.findElement(e.menuContent),$headingContainer:this.findElement(e.headingContainer),$menuItems:this.findElement(e.menuItem),$tabTitles:this.findElement(e.tabTitle),$tabDropdowns:this.findElement(e.tabDropdown),$anchorLink:this.findElement(e.anchorLink),$tabContentsBeforeInterlacing:this.findElement(e.tabContentBeforeInterlacing)}}getTabTitleFilterSelector(e){return`[${this.getSettings("dataAttributes").tabIndex}="${e}"]`}getTabIndex(e){return e.getAttribute(this.getSettings("dataAttributes").tabIndex)}setKeyboardNavigation(e){"Tab"===e.key&&(this.isKeyboardNavigation=!0)}dropdownMenuHeightControllerConfig(){const e=this.getSettings("selectors");return{elements:{$element:this.$element,$dropdownMenuContainer:this.$element.find(e.menuWrapper),$menuToggle:this.$element.find(e.dropdownMenuToggle)},attributes:{menuToggleState:"aria-expanded"},settings:{dropdownMenuContainerMaxHeight:"auto",menuHeightCssVarName:"--n-menu-dropdown-content-box-height"}}}handleContentContainerPosition(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.resetContentContainersPosition();const t=this.getSettings("ariaAttributes").activeTitleSelector,n=this.elements.$tabDropdowns.filter(t).attr("data-tab-index");e=e||this.elements.$tabContents.filter(this.getTabContentFilterSelector(n)),e.length&&this.setContentContainerAbsolutePosition(e)}setContentContainerAbsolutePosition(e){const t=this.getElementSettings(),n="fit_to_content"===t.content_width;if((0,o.isMenuInDropdownMode)(t))return;if(n){const t=elementorFrontend.config.is_rtl?"right":"left",n=0<this.getMenuItemContainerAbsolutePosition(e)?this.getMenuItemContainerAbsolutePosition(e):0;e.css(t,n)}const i=this.elements.$headingContainer[0].getBoundingClientRect().height;if(this.shouldPositionContentAbove(e,i)){const t=e[0].getBoundingClientRect();e.css({width:n?"max-content":"","max-width":t.width}),this.elements.$widgetContainer.addClass("content-above")}}getMenuItemContainerAbsolutePosition(e){const t=e.data("tab-index"),n=this.elements.$tabDropdowns.filter(this.getTabTitleFilterSelector(t))[0].closest(this.getSettings("selectors").tabTitle).getBoundingClientRect(),i=e[0].clientWidth;let o=null;switch(this.getElementSettings("content_horizontal_position")){case"left":o=this.getLeftDirectionContainerOffset(i,n);break;case"right":o=this.getRightDirectionContainerOffset(i,n);break;default:o=this.getCenteredContainerOffset(i,n)}return o}getCenteredContainerOffset(e,t){const n=e/2,i=elementorFrontend.elements.$body[0].clientWidth;let o=this.adjustForScrollbarIfNeeded(t.left+t.width/2);elementorFrontend.config.is_rtl&&(o=i-o);let s=o-n;return o+n>i?s=i-e:n>o&&(s=0),s}getLeftDirectionContainerOffset(e,t){return elementorFrontend.config.is_rtl?this.getRtlLeftDirectionContainerOffset(e,t):this.getLtrLeftDirectionContainerOffset(e,t)}getRtlLeftDirectionContainerOffset(e,t){const n=elementorFrontend.elements.$body[0].clientWidth;let i=n-this.adjustForScrollbarIfNeeded(t.left)-e;return-i+e>n&&(i=0),i}getLtrLeftDirectionContainerOffset(e,t){let n=this.adjustForScrollbarIfNeeded(t.left);return n=this.adjustStartOffsetToViewport(n,e),n}getRightDirectionContainerOffset(e,t){return elementorFrontend.config.is_rtl?this.getRtlRightDirectionContainerOffset(e,t):this.getLtrRightDirectionContainerOffset(e,t)}getRtlRightDirectionContainerOffset(e,t){let n=elementorFrontend.elements.$body[0].clientWidth-this.adjustForScrollbarIfNeeded(t.right);return n=this.adjustStartOffsetToViewport(n,e),n}adjustStartOffsetToViewport(e,t){const n=elementorFrontend.elements.$body[0].clientWidth;return e+t>n&&(e=n-t),e}getLtrRightDirectionContainerOffset(e,t){return e>t.right?0:t.right-e}adjustForScrollbarIfNeeded(e){if(elementorFrontend.config.is_rtl&&elementorFrontend.isEditMode()){e-=window.innerWidth-elementorFrontend.elements.$body[0].clientWidth}return e}getMenuContainerOffset(){const e=this.elements.$widgetContainer[0].getBoundingClientRect();return elementorFrontend.config.is_rtl?this.getMenuContainerOffsetRtl(e):e.left}getMenuContainerOffsetRtl(e){const t=elementorFrontend.elements.$body[0].clientWidth;let n=t-e.right;if(elementorFrontend.isEditMode()){n+=window.innerWidth-t}return n}resetContentContainersPosition(){this.elements.$tabContents.css({left:"",right:"",bottom:"",position:"var(--position)","max-width":"",width:"var(--width)"}),this.elements.$widgetContainer.removeClass("content-above")}getTabContentFilterSelector(e){return`[data-tab-index="${e}"]`}isActiveTab(e){return"true"===this.elements.$tabDropdowns.filter('[data-tab-index="'+e+'"]').attr(this.getSettings("ariaAttributes").titleStateAttribute)}activateDefaultTab(){const e=this.getSettings(),t=this.getEditSettings("activeItemIndex")||1,n={showTabFn:e.showTabFn,hideTabFn:e.hideTabFn};this.setSettings({showTabFn:"show",hideTabFn:"hide"}),this.changeActiveTab(t),this.setSettings(n),this.elements.$widgetContainer.addClass("e-activated")}activateTab(e){const t=this.getSettings(),n=t.classes.active,i=`.elementor-element-${this.getID()} .e-n-menu .e-n-menu .e-n-menu-dropdown-icon`,o=`.elementor-element-${this.getID()} .e-n-menu .e-n-menu .e-n-menu-content > .e-con`,s=this.elements.$tabDropdowns.filter(this.getTabTitleFilterSelector(e)).not(i),r="show"===t.showTabFn?0:400,a=this.elements.$tabContents.filter(this.getTabContentFilterSelector(e)).not(o);this.addAnimationToContentIfNeeded(e),a[t.showTabFn](r,(()=>this.onShowTabContent(a))),s.attr(this.getTitleActivationAttributes()),s.prev(".e-n-menu-title-container").find("a").attr(this.getTitleActivationAttributes("link")),a.addClass(n).parent().addClass(n),a.css({display:"var(--display)"}),a.removeAttr("display"),elementorFrontend.isEditMode()&&a.length&&this.activeContainerWidthListener(a),this.menuHeightController.reassignMenuHeight(a)}deactivateActiveTab(){const e=this.getSettings(),t=e.classes.active,n=e.ariaAttributes.activeTitleSelector,i="."+t,o=this.elements.$tabDropdowns.filter(n),s=this.elements.$tabContents.filter(i);this.setTabDeactivationAttributes(o),this.elements.$menuContent.removeClass(t),s.removeClass(t),s[e.hideTabFn](0,(()=>this.onHideTabContent(s))),this.removeAnimationFromContentIfNeeded(),elementorFrontend.isEditMode()&&s.length&&this.observedContainer?.unobserve(s[0]),this.menuHeightController.resetMenuHeight(s),this.clickInProgress=!0}getTitleActivationAttributes(){const e={};return"tab"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"tab")&&(e["aria-expanded"]="true"),e}setTabDeactivationAttributes(e){const t=this.getSettings("ariaAttributes").titleStateAttribute;e.attr(`${t}`,"false")}shouldPositionContentAbove(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const n=e[0].getBoundingClientRect();return this.isContentShorterThanItsTopOffset(n,t)&&this.isContentTallerThanItsBottomOffset(n)}isContentShorterThanItsTopOffset(e,t){return e.height<e.top-t}isContentTallerThanItsBottomOffset(e){return window.innerHeight-e.top<e.height}onShowTabContent(e){this.handleContentContainerPosition(e),elementorFrontend.elements.$window.trigger("elementor-pro/motion-fx/recalc"),elementorFrontend.elements.$window.trigger("elementor/nested-tabs/activate",e),elementorFrontend.elements.$window.trigger("elementor/bg-video/recalc")}onHideTabContent(){this.elements.$widgetContainer.hasClass("content-above")&&this.resetContentContainersPosition()}changeActiveTab(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.clickInProgress&&elementorFrontend.isEditMode()&&!n)return;const i=this.isActiveTab(e);this.deactivateActiveTab(),(!i||i&&!t)&&(this.clickInProgress=!0,this.activateTab(e)),setTimeout((()=>{this.clickInProgress=!1}))}changeActiveTabByKeyboard(e,t){if(t.widgetId.toString()!==this.getID().toString())return;if(!t.titleIndex)return void this.changeActiveTab("",!0,!0);const n=this.$element.find(`[data-focus-index="${t.titleIndex}"]`),i="a"===n[0].tagName.toLowerCase(),o=this.getSettings("selectors.tabDropdown"),s=i?n.next(o):n,r=this.getTabIndex(s[0]);this.changeActiveTab(r,!0,!0),e.stopPropagation()}onTabClick(e){elementorFrontend.isEditMode()&&e.preventDefault();const t=e?.currentTarget?.classList?.contains("link-only"),n=!this.isNeedToOpenOnClick()&&!this.isKeyboardNavigation;if(t||n)return;const i=this.getSettings("selectors"),o=e?.target?.closest(i.elementorWidgetWrapper)?.getAttribute("data-id");if(o!==this.getID().toString())return;const s=e?.currentTarget,r=s?.querySelector(i.tabDropdown),a=this.getTabIndex(r);this.changeActiveTab(a,!0)}bindEvents(){this.elements.$tabTitles.on(this.getTabEvents()),this.elements.$dropdownMenuToggle.on("click",this.onClickToggleDropdownMenu.bind(this)),this.elements.$tabContents.on(this.getContentEvents()),this.elements.$menuContent.on(this.getContentEvents()),this.elements.$headingContainer.on(this.getHeadingEvents()),elementorFrontend.addListenerOnce(this.getModelCID(),"scroll",elementorFrontend.debounce(this.menuHeightController.reassignMobileMenuHeight.bind(this.menuHeightController),250)),elementorFrontend.elements.$window.on("elementor/nested-tabs/activate",this.reInitSwipers),elementorFrontend.elements.$window.on("elementor/nested-elements/activate-by-keyboard",this.changeActiveTabByKeyboard.bind(this)),elementorFrontend.elements.$window.on("elementor/mega-menu/dropdown-toggle-by-keyboard",this.onClickToggleDropdownMenuByKeyboard.bind(this)),elementorFrontend.elements.$window.on("resize",this.resizeEventHandler.bind(this)),elementorFrontend.isEditMode()&&(this.addChildLifeCycleEventListeners(),elementorFrontend.elements.$window.on("elementor/dynamic/url_change",this.changeMegaMenuTitleContainerTag.bind(this))),elementorFrontend.elements.$window.on("elementor/nested-container/atomic-repeater",this.linkContainer.bind(this))}unbindEvents(){this.elements.$tabTitles.off(),this.elements.$menuContent.off(),this.elements.$tabContents.off(),this.elements.$headingContainer.off(),elementorFrontend.elements.$window.off("resize"),elementorFrontend.isEditMode()&&(this.removeChildLifeCycleEventListeners(),elementorFrontend.elements.$window.on("elementor/dynamic/url_change",this.changeMegaMenuTitleContainerTag.bind(this))),elementorFrontend.elements.$window.off("elementor/nested-tabs/activate",this.reInitSwipers),elementorFrontend.elements.$window.off("elementor/nested-elements/activate-by-keyboard",this.changeActiveTabByKeyboard.bind(this)),elementorFrontend.elements.$window.off("elementor/mega-menu/dropdown-toggle-by-keyboard",this.onClickToggleDropdownMenuByKeyboard.bind(this)),elementorFrontend.elements.$window.off("resize",this.resizeEventHandler.bind(this)),elementorFrontend.elements.$window.off("elementor/nested-container/atomic-repeater",this.linkContainer.bind(this))}reInitSwipers(e,t){const n=t.querySelectorAll(".swiper");for(const e of n){if(!e.swiper)return;e.swiper.initialized=!1,e.swiper.init()}}resizeEventHandler(){this.resizeListener=this.handleContentContainerPosition(),this.setLayoutType(),this.setTouchMode(),this.menuHeightController.reassignMobileMenuHeight(),this.setScrollPosition();const e=this.getSettings("ariaAttributes").activeTitleSelector,t=this.elements.$tabDropdowns.filter(e).attr("data-tab-index"),n=`.elementor-element-${this.getID()} .e-n-menu .e-n-menu .e-n-menu-content > .e-con`,i=this.elements.$tabContents.filter(this.getTabContentFilterSelector(t)).not(n);this.menuHeightController.resetMenuHeight(i),this.menuHeightController.reassignMenuHeight(i)}addChildLifeCycleEventListeners(){this.lifecycleChangeListener=this.handleContentContainerChildrenChanges.bind(this),window.addEventListener("elementor/editor/element-rendered",this.lifecycleChangeListener),window.addEventListener("elementor/editor/element-destroyed",this.lifecycleChangeListener)}removeChildLifeCycleEventListeners(){window.removeEventListener("elementor/editor/element-rendered",this.lifecycleChangeListener),window.removeEventListener("elementor/editor/element-destroyed",this.lifecycleChangeListener)}handleContentContainerChildrenChanges(e){this.isNestedElementRenderedInContentContainer(e.detail.elementView)&&this.handleContentContainerPosition()}isNestedElementRenderedInContentContainer(e){const t=e?.getContainer();if(!t)return!1;return t.getParentAncestry().some((e=>this.getID().toString()===e.model.get("id").toString()))}getTabEvents(){const e={click:this.onTabClick.bind(this)};return this.isNeedToOpenOnClick()?e:this.replaceClickWithHover(e)}getContentEvents(){return this.isNeedToOpenOnClick()?{}:{mouseleave:this.onMouseLeave.bind(this),mousemove:this.trackMousePosition.bind(this)}}isNeedToOpenOnClick(){const e=this.getElementSettings();return this.isEdit||this.isMobileDevice()||"hover"!==e.open_on||"dropdown"===e.item_layout}isMobileDevice(){return["mobile","mobile_extra","tablet","tablet_extra"].includes(elementorFrontend.getCurrentDeviceMode())}replaceClickWithHover(e){return e.mouseenter=this.onMouseTitleEnter.bind(this),e.mouseleave=this.onMouseLeave.bind(this),e.keyup=this.setKeyboardNavigation.bind(this),e}onMouseTitleEnter(e){e.preventDefault();const t=this.getSettings(),n=e?.currentTarget,i=n?.closest(t.selectors.elementorWidgetWrapper)?.getAttribute("data-id");if(this.$element[0].getAttribute("data-id")!==i)return;const o=t.ariaAttributes.titleStateAttribute,s=t.selectors.tabDropdown,r=n?.querySelector(s);if("true"===r?.getAttribute(o))return;const a=r?.getAttribute("data-tab-index");this.changeActiveTab(a,!0)}onClickToggleDropdownMenu(e){this.elements.$widgetContainer.attr("data-layout","dropdown");const t=this.getSettings("ariaAttributes").titleStateAttribute,n="true"===this.elements.$dropdownMenuToggle.attr(t);"boolean"!=typeof e&&(e=!n);const i=e?"true":"false";this.elements.$dropdownMenuToggle.attr(t,i),elementorFrontend.utils.events.dispatch(window,"elementor-pro/mega-menu/dropdown-open"),this.menuHeightController.reassignMobileMenuHeight()}onClickOutsideDropdownMenu(e){if(!this.isNeedToOpenOnClick())return;const t=this.getSettings(),n=t.selectors,i=`.elementor-element-${this.getID()}`,o=`> .e-con.${t.classes.active}`,s=0===this.elements.$menuContent.find(o).length,r=elementorFrontend.isEditMode()&&!document.body.contains(e?.target),a=!!e?.target?.closest(`${i} ${n.widgetContainer}`),l=e?.target?.classList?.contains(n.menuContent.replace(".",""));l?this.deactivateActiveTab():s||a||r||this.deactivateActiveTab()}onClickToggleDropdownMenuByKeyboard(e,t){t.widgetId.toString()===this.getID().toString()&&this.onClickToggleDropdownMenu(t.show)}addAnimationToContentIfNeeded(e){const t=this.getElementSettings("open_animation");if("none"===t||""===t)return;this.elements.$tabContents.filter(this.getTabContentFilterSelector(e)).addClass(`animated ${t}`)}removeAnimationFromContentIfNeeded(){const e=this.getElementSettings("open_animation");"none"!==e&&""!==e&&this.elements.$tabContents.removeClass(`animated ${e}`)}trackMousePosition(e){this.prevMouseY=e?.clientY}isMenuContentHovered(){const e=this.getSettings();return this.$element.find(`${e.selectors.menuContent}:hover`).length>0}isCursorInBetweenMenuTitleAndContent(e){const t=this.getSettings(),n=t.selectors,i=e?.currentTarget,o=i?.closest(n.menuItem)?.querySelector(n.menuContent),s=i.classList?.contains(n.tabTitle.replace(".","")),r=o?.classList?.contains(t.classes.active);if(!s||!r)return!1;const a=i.getBoundingClientRect(),l=o.getBoundingClientRect(),d=e.clientY;return a.bottom<=l.top?d>=a.bottom&&d<l.top:d<=a.top&&d>l.bottom}didCursorMoveSidewaysOrDown(e){return null!==this.prevMouseY&&e?.clientY>=this.prevMouseY}isHoveredDropdownMenu(e,t){return(!e||!this.didCursorMoveSidewaysOrDown(t))&&this.isMenuContentHovered()}onMouseLeave(e){e.preventDefault();const t=e?.currentTarget?.classList?.contains("e-con");this.isHoveredDropdownMenu(t,e)||this.isCursorInBetweenMenuTitleAndContent(e)||this.deactivateActiveTab()}onInit(){if(this.menuHeightController=new elementorProFrontend.utils.DropdownMenuHeightController(this.dropdownMenuHeightControllerConfig()),super.onInit(...arguments),this.getSettings("autoExpand")&&this.activateDefaultTab(),(0,r.setHorizontalScrollAlignment)(this.getHorizontalScrollingSettings()),this.setTouchMode(),!elementorFrontend.isEditMode()){const e=this.getSettings("classes");this.anchorLinks=new s.default(this.elements.$anchorLink,e),this.anchorLinks.initialize(),elementorFrontend.elements.$window.on("elementor/dynamic/url_change",this.changeMegaMenuTitleContainerTag.bind(this))}this.menuToggleVisibilityListener(this.elements.$dropdownMenuToggle),this.setScrollPosition(),this.onClickOutsideDropdownMenu=this.onClickOutsideDropdownMenu.bind(this),document.addEventListener("click",this.onClickOutsideDropdownMenu),this.clickInProgress=!1}onDestroy(){document.removeEventListener("click",this.onClickOutsideDropdownMenu),elementorFrontend.elements.$window.off("elementor/dynamic/url_change")}setScrollPosition(){const e={element:this.elements.$headingContainer[0],direction:this.getItemPosition(),justifyCSSVariable:"--n-menu-heading-justify-content",horizontalScrollStatus:this.getHorizontalScrollSetting()};(0,r.setHorizontalScrollAlignment)(e)}getPropsThatTriggerContentPositionCalculations(){return["content_horizontal_position","content_position","item_position_horizontal","content_width","item_layout"]}activeContainerWidthListener(e){let t=0;this.observedContainer=new ResizeObserver((e=>{const n=e[0].borderBoxSize?.[0].inlineSize;n&&n!==t&&(t=n,0!==t&&this.handleContentContainerPosition())})),this.observedContainer.observe(e[0])}menuToggleVisibilityListener(e){let t;this.observedContainer=new ResizeObserver((e=>{const n=e[0].borderBoxSize?.[0].inlineSize;n!==t&&(t=n,this.setLayoutType())})),this.observedContainer.observe(e[0])}onElementChange(e){this.getPropsThatTriggerContentPositionCalculations().includes(e)&&this.handleContentContainerPosition(),this.setLayoutType()}onEditSettingsChange(e,t){this.getSettings().autoFocus&&"activeItemIndex"===e&&this.changeActiveTab(t,!1),this.setLayoutType()}setLayoutType(){const e="none"===this.elements.$dropdownMenuToggle.css("display")?"horizontal":"dropdown";this.elements.$widgetContainer.attr("data-layout",e)}getHeadingEvents(){const e=this.elements.$headingContainer[0];return{mousedown:this.changeScrollStatusAndDispatch.bind(this,e),mouseup:this.changeScrollStatusAndDispatch.bind(this,e),mouseleave:this.changeScrollStatusAndDispatch.bind(this,e),mousemove:this.setHorizontalTitleScrollValuesAndDispatch.bind(this,e)}}getHorizontalScrollSetting(){const e=elementorFrontend.getCurrentDeviceMode();return elementorFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),"horizontal_scroll","",e)}getItemPosition(){const e=elementorFrontend.getCurrentDeviceMode();return elementorFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),"item_position_horizontal","",e)}changeScrollStatusAndDispatch(e,t){(0,r.changeScrollStatus)(e,t),elementorFrontend.elements.$window.trigger("elementor-pro/mega-menu/heading-mouse-event")}setHorizontalTitleScrollValuesAndDispatch(e,t){(0,r.setHorizontalTitleScrollValues)(e,this.getHorizontalScrollSetting(),t),elementorFrontend.elements.$window.trigger("elementor-pro/mega-menu/heading-mouse-event")}linkContainer(e){const{container:t}=e.detail,n=t.model.get("id"),i=String(this.$element.data("id")),o=t.view.$el;n===i&&(this.updateIndexValues(o),this.updateListeners(o))}updateIndexValues(e){const{selectors:{directTabTitle:t,directTabContent:n}}=this.getDefaultSettings(),i=e[0],o=i.querySelectorAll(n),s=i.querySelectorAll(t),r=this.getSettings(),a=s[0].getAttribute("id").slice(0,-1);s.forEach(((e,t)=>{const n=t+1,i=a+n,s=i.replace("e-n-menu-title-","e-n-menu-content-"),l=i.replace("e-n-menu-title-","e-n-menu-dropdown-icon-");e.setAttribute("id",i),e.querySelector(r.selectors.tabDropdown)?.setAttribute("data-tab-index",n),e.querySelector(r.selectors.tabDropdown)?.setAttribute("id",l),e.querySelector(r.selectors.tabDropdown)?.setAttribute("aria-controls",s),e.querySelector(r.selectors.tabTitleText)?.setAttribute("data-binding-index",n),o[t]?.setAttribute("aria-labelledby",l),o[t]?.setAttribute("data-tab-index",n),o[t]?.setAttribute("id",s)}))}updateListeners(e){const{selectors:{tabClickableTitle:t,tabDropdown:n,tabContent:i,tabTitle:o}}=this.getSettings(),s=e.find(o),r=e.find(t);this.elements.$tabTitles=e.find(t),this.elements.$tabDropdowns=e.find(n),this.elements.$tabContents=e.find(i),s.off(),r.on(this.getTabEvents()),this.clickInProgress=!1}changeMegaMenuTitleContainerTag(e){const{element:t,actionName:n,value:i}=e.detail,o=t.parentNode,s=o.parentNode,r=this.maybeCreateNewElement(o,i),a=this.maybeReplaceMenuItemTitleContent(o,r,s),l=t.dataset?.currentUrl||null;this.maybeUpdateNewElementsHref(i,a),this.eCurrentClassHandler(n,s,l===i)}maybeReplaceMenuItemTitleContent(e,t,n){return t?(Array.from(e.attributes).forEach((e=>{t.setAttribute(e.name,e.value)})),"A"===t.tagName?t.classList.add("e-link","e-focus"):"DIV"===t.tagName&&t.classList.remove("e-link","e-focus"),t.innerHTML=e.innerHTML,n.replaceChild(t,e),t):e}maybeCreateNewElement(e,t){return t?t&&"DIV"===e.tagName?document.createElement("a"):void 0:document.createElement("div")}maybeUpdateNewElementsHref(e,t){e?t.setAttribute("href",e):t.removeAttribute("href")}eCurrentClassHandler(e,t,n){const i=this.getSettings(),{classes:{activeAnchorItem:o},postUrl:s,internalUrl:r}=i;switch(e){case s:t.classList.add(o);break;case r:n?t.classList.add(o):t.classList.remove(o);break;default:t.classList.contains(o)&&s!==e&&t.classList.remove(o)}}setTouchMode(){const e=this.getSettings("selectors").widgetContainer;if(elementorFrontend.isEditMode()||"resize"===event?.type){const t=["mobile","mobile_extra","tablet","tablet_extra"],n=elementorFrontend.getCurrentDeviceMode();if(-1!==t.indexOf(n))return void this.$element.find(e).attr("data-touch-mode","true")}else if("ontouchstart"in window)return void this.$element.find(e).attr("data-touch-mode","true");this.$element.find(e).attr("data-touch-mode","false")}getTabsDirection(){const e=elementorFrontend.getCurrentDeviceMode();return elementorFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),"tabs_justify_horizontal","",e)}getHorizontalScrollingSettings(){return{element:this.elements.$headingContainer[0],direction:this.getTabsDirection(),justifyCSSVariable:"--n-tabs-heading-justify-content",horizontalScrollStatus:this.getHorizontalScrollSetting()}}}t.default=MegaMenu},166:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isMenuInDropdownMode=function isMenuInDropdownMode(e){if("dropdown"===e.item_layout)return!0;const t=elementorFrontend.breakpoints.getActiveBreakpointsList({withDesktop:!0}),n=t.indexOf(e.breakpoint_selector);return t.indexOf(elementorFrontend.getCurrentDeviceMode())<=n}}}]);