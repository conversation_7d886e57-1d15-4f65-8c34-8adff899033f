/*! elementor-pro - v3.30.0 - 01-07-2025 */
"use strict";(self.webpackChunkelementor_pro=self.webpackChunkelementor_pro||[]).push([[798],{9319:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=elementorModules.frontend.handlers.Base.extend({getDefaultSettings:()=>({selectors:{wrapper:".elementor-search-form",container:".elementor-search-form__container",icon:".elementor-search-form__icon",input:".elementor-search-form__input",toggle:".elementor-search-form__toggle",submit:".elementor-search-form__submit",closeButton:".dialog-close-button"},classes:{isFocus:"elementor-search-form--focus",isFullScreen:"elementor-search-form--full-screen",lightbox:"elementor-lightbox"}}),getDefaultElements(){var e=this.getSettings("selectors"),t={};return t.$wrapper=this.$element.find(e.wrapper),t.$container=this.$element.find(e.container),t.$input=this.$element.find(e.input),t.$icon=this.$element.find(e.icon),t.$toggle=this.$element.find(e.toggle),t.$submit=this.$element.find(e.submit),t.$closeButton=this.$element.find(e.closeButton),t},bindEvents(){var e=this,t=e.elements.$container,s=e.elements.$closeButton,n=e.elements.$input,o=e.elements.$wrapper,l=e.elements.$icon,r=e.elements.$toggle,i=this.getElementSettings("skin"),c=this.getSettings("classes");const triggerClickOnEnterSpace=e=>{13!==e.keyCode&&32!==e.keyCode||(e.currentTarget.click(),e.stopPropagation())};"full_screen"===i?(r.on("click",(()=>(t.addClass(c.isFullScreen).addClass(c.lightbox),void n.trigger("focus")))).on("keyup",(e=>triggerClickOnEnterSpace(e))),t.on("click",(function(e){t.hasClass(c.isFullScreen)&&t[0]===e.target&&t.removeClass(c.isFullScreen).removeClass(c.lightbox)})),s.on("click",(()=>(t.removeClass(c.isFullScreen).removeClass(c.lightbox),void r.trigger("focus")))).on("keyup",(e=>triggerClickOnEnterSpace(e))),elementorFrontend.elements.$document.on("keyup",(function(e){27===e.keyCode&&t.hasClass(c.isFullScreen)&&t.trigger("click")}))):n.on({focus(){o.addClass(c.isFocus)},blur(){o.removeClass(c.isFocus)}}),"minimal"===i&&l.on("click",(function(){o.addClass(c.isFocus),n.trigger("focus")}))}})}}]);