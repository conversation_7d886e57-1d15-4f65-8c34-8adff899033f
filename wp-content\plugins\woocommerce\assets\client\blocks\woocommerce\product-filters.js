import*as e from"@wordpress/interactivity";var t={438:e=>{e.exports=import("@wordpress/interactivity-router")}},r={};function i(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}};return t[e](a,a.exports,i),a.exports}i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const o=(p={getConfig:()=>e.getConfig,getContext:()=>e.getContext,getServerContext:()=>e.getServerContext,store:()=>e.store},v={},i.d(v,p),v),{getContext:a,store:n,getServerContext:s,getConfig:l}=o,c="woocommerce/product-filters";var p,v;const u={state:{get params(){const{activeFilters:e}=a(),t={};function r(e,r){if(e in t&&t[e].length>0)return t[e]=`${t[e]},${r}`;t[e]=r}return e.forEach((e=>{const{type:i,value:o}=e;if(o){if("price"===i){const[e,r]=o.split("|");e&&(t.min_price=e),r&&(t.max_price=r)}if("status"===i&&r("filter_stock_status",o),"rating"===i&&r("rating_filter",o),i.includes("attribute")){const[,a]=i.split("/");r(`filter_${a}`,o),t[`query_type_${a}`]=e.attributeQueryType||"or"}}})),t},get activeFilters(){const{activeFilters:e}=a();return e.filter((e=>!!e.value)).sort(((e,t)=>e.activeLabel.toLowerCase().localeCompare(t.activeLabel.toLowerCase()))).map((e=>({...e,uid:`${e.type}/${e.value}`})))},get isFilterSelected(){const{activeFilters:e,item:t}=a();return e.some((e=>e.type===t.type&&e.value===t.value))}},actions:{openOverlay:()=>{if(a().isOverlayOpened=!0,document.getElementById("wpadminbar")){const e=(document.documentElement||document.body.parentNode||document.body).scrollTop;document.body.style.setProperty("--adminbar-mobile-padding",`max(calc(var(--wp-admin--admin-bar--height) - ${e}px), 0px)`)}},closeOverlay:()=>{a().isOverlayOpened=!1},closeOverlayOnEscape:e=>{a().isOverlayOpened&&"Escape"===e.key&&y.closeOverlay()},removeActiveFiltersBy:e=>{const t=a();t.activeFilters=t.activeFilters.filter((t=>!e(t)))},toggleFilter:()=>{m.isFilterSelected?function(){const{item:e}=a();y.removeActiveFiltersBy((t=>t.type===e.type&&t.value===e.value))}():function(){const e=a(),t={value:e.item.value,type:e.item.type,attributeQueryType:e.item.attributeQueryType,activeLabel:e.activeLabelTemplate.replace("{{label}}",e.item?.ariaLabel||e.item.label)},r=e.activeFilters.filter((e=>!(e.value===t.value&&e.type===t.type)));r.push(t),e.activeFilters=r}(),y.navigate()},*navigate(){const e=s?s():a(),t=l(c).canonicalUrl,r=new URL(t),{searchParams:o}=r;for(const t in e.params)o.delete(t);for(const e in m.params)o.set(e,m.params[e]);if(window.location.href===r.href)return;const n=l("woocommerce"),p=l(c),v=n?.isBlockTheme||!1,u=p?.isProductArchive||!1;if(n?.needsRefreshForInteractivityAPI||!v&&u)return window.location.href=r.href;const y=yield Promise.resolve().then(i.bind(i,438));yield y.actions.navigate(r.href)}},callbacks:{scrollLimit:()=>{const{isOverlayOpened:e}=a();document.body.style.overflow=e?"hidden":"auto"}}},{state:m,actions:y}=n(c,u);