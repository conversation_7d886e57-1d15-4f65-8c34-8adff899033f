!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.JetpackConnection=t():e.JetpackConnection=t()}(globalThis,(()=>(()=>{var e={7689:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});const o={error:"TcCZnGE6mad8Dvz9pCZi",button:"_mn6o2Dtm5pfFWc8_A1K"}},2258:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});const o={button:"zI5tJ_qhWE6Oe6Lk75GY","is-icon-button":"tuBt2DLqimiImoqVzPqo",small:"Na39I683LAaSA99REg14",normal:"ipS7tKy9GntCS4R3vekF",icon:"paGLQwtPEaJmtArCcmyK",regular:"lZAo6_oGfclXOO9CC6Rd","full-width":"xJDOiJxTt0R_wSl8Ipz_",loading:"q_tVWqMjl39RcY6WtQA6","external-icon":"CDuBjJp_8jxzx5j6Nept"}},9535:()=>{},8403:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});const o={sm:"(max-width: 599px)",md:"(min-width: 600px) and (max-width: 959px)",lg:"(min-width: 960px)"}},6406:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});const o={placeholder:"NisihrgiIKl_knpYJtfg",pulse:"R2i0K45dEF157drbVRPI"}},4319:()=>{},8325:()=>{},9634:()=>{},4495:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});const o={reset:"WQVtrU6q0L1Igcj7wCrQ","headline-medium":"UujoBFTnQNY2cWU2SIsH","headline-small":"TeGO5V_thHw5lDAm1_2M","headline-small-regular":"WolQzb2MsSgiNmLtc7_j","title-medium":"hUB0JT8p1T2Hw28N6qC8","title-medium-semi-bold":"gKZWDv5chz3_O3Syp74H","title-small":"zY2No8Ga4b8shbOQGhnv",body:"tIj0D1t8Cc892ikmgFPZ","body-small":"KdcN0BnOaVeVhyLRKqhS","body-extra-small":"dso3Rh3tl3Xv1GumBktz","body-extra-small-bold":"mQ1UlbN9u4Mg9byO8m7v",label:"PItlW5vRExLnTj4a8eLE","m-0":"TwRpPlktzxhmFVeua7P5","mx-0":"zVfqx7gyb3o9mxfGynn1","my-0":"iSHVzNiB9iVleGljaQxy","mt-0":"xqDIp6cNVr_E6RXaiPyD","mr-0":"S8EwaXk1kyPizt6x4WH2","mb-0":"ODX5Vr1TARoLFkDDFooD","ml-0":"cphJ8dCpfimnky7P2FHg","m-1":"PFgIhNxIyiSuNvQjAIYj","mx-1":"M2jKmUzDxvJjjVEPU3zn","my-1":"io15gAh8tMTNbSEfwJKk","mt-1":"rcTN5uw9xIEeMEGL3Xi_","mr-1":"CQSkybjq2TcRM1Xo9COV","mb-1":"hfqOWgq6_MEGdFE82eOY","ml-1":"I8MxZQYTbuu595yfesWA","m-2":"kQkc6rmdpvLKPkyoJtVQ","mx-2":"j6vFPxWuu4Jan2ldoxpp","my-2":"hqr39dC4H_AbactPAkCG","mt-2":"c3dQnMi16C6J6Ecy4283","mr-2":"YNZmHOuRo6hU7zzKfPdP","mb-2":"Db8lbak1_wunpPk8NwKU","ml-2":"ftsYE5J9hLzquQ0tA5dY","m-3":"Det4MHzLUW7EeDnafPzq","mx-3":"h_8EEAztC29Vve1datb5","my-3":"YXIXJ0h1k47u6hzK8KcM","mt-3":"soADBBkcIKCBXzCTuV9_","mr-3":"zSX59ziEaEWGjnpZa4uV","mb-3":"yrVTnq_WBMbejg89c2ZQ","ml-3":"UKtHPJnI2cXBWtPDm5hM","m-4":"guexok_Tqd5Tf52hRlbT","mx-4":"oS1E2KfTBZkJ3F0tN7T6","my-4":"DN1OhhXi6AoBgEdDSbGd","mt-4":"ot2kkMcYHv53hLZ4LSn0","mr-4":"A1krOZZhlQ6Sp8Cy4bly","mb-4":"pkDbXXXL32237M0hokEh","ml-4":"XXv4kDTGvEnQeuGKOPU3","m-5":"yGqHk1a57gaISwkXwXe6","mx-5":"X8cghM358X3DkXLc9aNK","my-5":"GdfSmGwHlFnN2S6xBn1f","mt-5":"yqeuzwyGQ7zG0avrGqi_","mr-5":"g9emeCkuHvYhveiJbfXO","mb-5":"Lvk3dqcyHbZ07QCRlrUQ","ml-5":"r3yQECDQ9qX0XZzXlVAg","m-6":"aQhlPwht2Cz1X_63Miw0","mx-6":"JyHb0vK3wJgpblL9s5j8","my-6":"cY2gULL1lAv6WPNIRuf3","mt-6":"NBWQ9Lwhh_fnry3lg_p7","mr-6":"yIOniNe5E40C8fWvBm5V","mb-6":"t30usboNSyqfQWIwHvT3","ml-6":"Nm_TyFkYCMhOoghoToKJ","m-7":"C4qJKoBXpgKtpmrqtEKB","mx-7":"S93Srbu6NQ_PBr7DmTiD","my-7":"fJj8k6gGJDks3crUZxOS","mt-7":"cW6D6djs7Ppm7fD7TeoV","mr-7":"DuCnqNfcxcP3Z__Yo5Ro","mb-7":"im8407m2fw5vOg7O2zsw","ml-7":"G0fbeBgvz2sh3uTP9gNl","m-8":"kvW3sBCxRxUqz1jrVMJl","mx-8":"tOjEqjLONQdkiYx_XRnw","my-8":"op5hFSx318zgxsoZZNLN","mt-8":"c9WfNHP6TFKWIfLxv52J","mr-8":"sBA75QqcqRwwYSHJh2wc","mb-8":"GpL6idrXmSOM6jB8Ohsf","ml-8":"HbtWJoQwpgGycz8dGzeT","p-0":"uxX3khU88VQ_Ah49Ejsa","px-0":"KX0FhpBKwKzs9fOUdbNz","py-0":"PfK8vKDyN32dnimlzYjz","pt-0":"emxLHRjQuJsImnPbQIzE","pr-0":"kJ8WzlpTVgdViXt8ukP9","pb-0":"tg_UIUI11VBzrTAn2AzJ","pl-0":"uczvl8kaz84oPQJ2DB2R","p-1":"o7UHPcdVK3lt7q3lqV4o","px-1":"IDqEOxvDoYrFYxELPmtX","py-1":"DdywPW2qSYlu2pt8tpO2","pt-1":"npy3hw4A5QSkDicb2CJJ","pr-1":"LgbptTApNY5NwLQvEFAt","pb-1":"WZQy2SZuZso59bUsXXyl","pl-1":"o331apInxNunbYB3SfPE","p-2":"fMPIyD9Vqki1Lrc_yJnG","px-2":"i2pMcTcdrr10IQoiSm_L","py-2":"eA702gn32kwptiI1obXH","pt-2":"o9bGieUKcYc8o0Ij9oZX","pr-2":"SwZcFez1RDqWsOFjB5iG","pb-2":"eHpLc_idmuEqeqCTvqkN","pl-2":"vU39i2B4P1fUTMB2l6Vo","p-3":"JHWNzBnE29awhdu5BEh1","px-3":"X72lGbb56L3KFzC2xQ9N","py-3":"BzfNhRG8wXdCEB5ocQ6e","pt-3":"srV0KSDC83a2fiimSMMQ","pr-3":"lUWfkmbQjCskhcNwkyCm","pb-3":"Ts0dIlc3aTSL7V4cIHis","pl-3":"CzlqQXXhX6MvorArFZ8B","p-4":"TqMPkQtR_DdZuKb5vBoV","px-4":"a7UrjhI69Vetlcj9ZVzz","py-4":"StEhBzGs2Gi5dDEkjhAv","pt-4":"FGneZfZyvYrt1dG0zcnm","pr-4":"APEH216rpdlJWgD2fHc8","pb-4":"oGwXC3ohCic9XnAj6x69","pl-4":"U6gnT9y42ViPNOcNzBwb","p-5":"IpdRLBwnHqbqFrixgbYC","px-5":"HgNeXvkBa9o3bQ5fvFZm","py-5":"tJtFZM3XfPG9v9TSDfN1","pt-5":"PdifHW45QeXYfK568uD8","pr-5":"mbLkWTTZ0Za_BBbFZ5b2","pb-5":"vVWpZpLlWrkTt0hMk8XU","pl-5":"RxfaJj5a1Nt6IavEo5Zl","p-6":"SppJULDGdnOGcjZNCYBy","px-6":"palY2nLwdoyooPUm9Hhk","py-6":"WYw1JvZC0ppLdvSAPhr_","pt-6":"YEEJ9b90ueQaPfiU8aeN","pr-6":"QE0ssnsKvWJMqlhPbY5u","pb-6":"n8yA3jHlMRyLd5UIfoND","pl-6":"tXHmxYnHzbwtfxEaG51n","p-7":"kBTsPKkO_3g_tLkj77Um","px-7":"RyhrFx6Y1FGDrGAAyaxm","py-7":"CBwRpB0bDN3iEdQPPMJO","pt-7":"vQVSq6SvWKbOMu6r4H6b","pr-7":"oBy5__aEADMsH46mrgFX","pb-7":"KVEXoJqf1s92j0JMdNmN","pl-7":"ZMXGNrNaKW3k_3TLz0Fq","p-8":"tuiR9PhkHXhGyEgzRZRI","px-8":"U7454qyWkQNa2iaSJziu","py-8":"VLYIv2GVocjuN93e8HC8","pt-8":"X1rm9DQ1zLGLfogja5Gn","pr-8":"JS7G6kAuqJo5GIuF8S5t","pb-8":"Y8F9ga1TDCMbM1lj4gUz","pl-8":"AJuyNGrI63BOWql719H8"}},1772:()=>{},9064:()=>{},381:()=>{},4175:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});const o={heading:"urouayitSUT8zW0V3p_0",notice:"iXXJlk08gFDeCvsTTlNQ",button:"MWqRqr7q6fgvLxitcWYk","bigger-than-medium":"YLcXAoc82nypTPaKSAcd",error:"e6hHy8BZ7ZKPSXbIC0UG",message:"jXz8LnXNzMDdtHqkG0sZ"}},7419:()=>{},785:()=>{},255:()=>{},3732:()=>{},2057:()=>{},1127:()=>{},7750:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var o=n(6087);const c=(0,o.forwardRef)((function({icon:e,size:t=24,...n},c){return(0,o.cloneElement)(e,{width:t,height:t,...n,ref:c})}))},1386:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var o=n(5573),c=n(790);const r=(0,c.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(o.Path,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"})})},8391:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var o=n(5573),c=n(790);const r=(0,c.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(o.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})})},4804:(e,t,n)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let o=0,c=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(o++,"%c"===e&&(c=o))})),t.splice(c,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=n(5067)(t);const{formatters:o}=e.exports;o.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},5067:(e,t,n)=>{e.exports=function(e){function t(e){let n,c,r,s=null;function i(...e){if(!i.enabled)return;const o=i,c=Number(new Date),r=c-(n||c);o.diff=r,o.prev=n,o.curr=c,n=c,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,c)=>{if("%%"===n)return"%";s++;const r=t.formatters[c];if("function"==typeof r){const t=e[s];n=r.call(o,t),e.splice(s,1),s--}return n})),t.formatArgs.call(o,e);(o.log||t.log).apply(o,e)}return i.namespace=e,i.useColors=t.useColors(),i.color=t.selectColor(e),i.extend=o,i.destroy=t.destroy,Object.defineProperty(i,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(c!==t.namespaces&&(c=t.namespaces,r=t.enabled(e)),r),set:e=>{s=e}}),"function"==typeof t.init&&t.init(i),i}function o(e,n){const o=t(this.namespace+(void 0===n?":":n)+e);return o.log=this.log,o}function c(e,t){let n=0,o=0,c=-1,r=0;for(;n<e.length;)if(o<t.length&&(t[o]===e[n]||"*"===t[o]))"*"===t[o]?(c=o,r=n,o++):(n++,o++);else{if(-1===c)return!1;o=c+1,r++,n=r}for(;o<t.length&&"*"===t[o];)o++;return o===t.length}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const n=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of n)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const n of t.skips)if(c(e,n))return!1;for(const n of t.names)if(c(e,n))return!0;return!1},t.humanize=n(3594),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((n=>{t[n]=e[n]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}},3594:e=>{var t=1e3,n=60*t,o=60*n,c=24*o,r=7*c,s=365.25*c;function i(e,t,n,o){var c=t>=1.5*n;return Math.round(e/n)+" "+o+(c?"s":"")}e.exports=function(e,a){a=a||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var i=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!i)return;var a=parseFloat(i[1]);switch((i[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return a*s;case"weeks":case"week":case"w":return a*r;case"days":case"day":case"d":return a*c;case"hours":case"hour":case"hrs":case"hr":case"h":return a*o;case"minutes":case"minute":case"mins":case"min":case"m":return a*n;case"seconds":case"second":case"secs":case"sec":case"s":return a*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}(e);if("number"===l&&isFinite(e))return a.long?function(e){var r=Math.abs(e);if(r>=c)return i(e,r,c,"day");if(r>=o)return i(e,r,o,"hour");if(r>=n)return i(e,r,n,"minute");if(r>=t)return i(e,r,t,"second");return e+" ms"}(e):function(e){var r=Math.abs(e);if(r>=c)return Math.round(e/c)+"d";if(r>=o)return Math.round(e/o)+"h";if(r>=n)return Math.round(e/n)+"m";if(r>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},1583:(e,t,n)=>{"use strict";var o=n(1752);function c(){}function r(){}r.resetWarningCache=c,e.exports=function(){function e(e,t,n,c,r,s){if(s!==o){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:r,resetWarningCache:c};return n.PropTypes=n,n}},3619:(e,t,n)=>{e.exports=n(1583)()},1752:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},372:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var o=n(4804);const c=n.n(o)()("dops:analytics");let r,s;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const i={initialize:function(e,t,n){i.setUser(e,t),i.setSuperProps(n),i.identifyUser()},setGoogleAnalyticsEnabled:function(e,t=null){this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){s={ID:e,username:t}},setSuperProps:function(e){r=e},assignSuperProps:function(e){r=Object.assign(r||{},e)},mc:{bumpStat:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);c("Bumping stats %o",e)}else n="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),c('Bumping stat "%s" in group "%s"',t,e);return n}(e,t);i.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+n+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);c("Built stats %o",e)}else n="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),c('Built stat "%s" in group "%s"',t,e);return n}(e,t);i.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+n+"&t="+Math.random())}},pageView:{record:function(e,t){i.tracks.recordPageView(e),i.ga.recordPageView(e,t)}},purchase:{record:function(e,t,n,o,c,r,s){i.ga.recordPurchase(e,t,n,o,c,r,s)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(r&&(c("- Super Props: %o",r),t=Object.assign(t,r)),c('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):c('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};i.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){i.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){c("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};i.ga.initialized||(s&&(e={userId:"u-"+s.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),i.ga.initialized=!0)},recordPageView:function(e,t){i.ga.initialize(),c("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,n,o){i.ga.initialize();let r="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==n&&(r+=" [Option Label: "+n+"]"),void 0!==o&&(r+=" [Option Value: "+o+"]"),c(r),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,n,o)},recordPurchase:function(e,t,n,o,c,r,s){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:o,currency:s}),window.ga("ecommerce:addItem",{id:e,name:t,sku:n,price:c,quantity:r}),window.ga("ecommerce:send")}},identifyUser:function(){s&&window._tkq.push(["identifyUser",s.ID,s.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},a=i},5932:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>p});var o=n(6439),c=n(3832);function r(e){class t extends Error{constructor(...t){super(...t),this.name=e}}return t}const s=r("JsonParseError"),i=r("JsonParseAfterRedirectError"),a=r("Api404Error"),l=r("Api404AfterRedirectError"),d=r("FetchNetworkError");const p=new function(e,t){let n=e,r=e,s={"X-WP-Nonce":t},i={credentials:"same-origin",headers:s},a={method:"post",credentials:"same-origin",headers:Object.assign({},s,{"Content-type":"application/json"})},l=function(e){const t=e.split("?"),n=t.length>1?t[1]:"",o=n.length?n.split("&"):[];return o.push("_cacheBuster="+(new Date).getTime()),t[0]+"?"+o.join("&")};const d={setApiRoot(e){n=e},setWpcomOriginApiUrl(e){r=e},setApiNonce(e){s={"X-WP-Nonce":e},i={credentials:"same-origin",headers:s},a={method:"post",credentials:"same-origin",headers:Object.assign({},s,{"Content-type":"application/json"})}},setCacheBusterCallback:e=>{l=e},registerSite:(e,t,c)=>{const r={};return(0,o.jetpackConfigHas)("consumer_slug")&&(r.plugin_slug=(0,o.jetpackConfigGet)("consumer_slug")),null!==t&&(r.redirect_uri=t),c&&(r.from=c),g(`${n}jetpack/v4/connection/register`,a,{body:JSON.stringify(r)}).then(u).then(m)},fetchAuthorizationUrl:e=>p((0,c.addQueryArgs)(`${n}jetpack/v4/connection/authorize_url`,{no_iframe:"1",redirect_uri:e}),i).then(u).then(m),fetchSiteConnectionData:()=>p(`${n}jetpack/v4/connection/data`,i).then(m),fetchSiteConnectionStatus:()=>p(`${n}jetpack/v4/connection`,i).then(m),fetchSiteConnectionTest:()=>p(`${n}jetpack/v4/connection/test`,i).then(u).then(m),fetchUserConnectionData:()=>p(`${n}jetpack/v4/connection/data`,i).then(m),fetchUserTrackingSettings:()=>p(`${n}jetpack/v4/tracking/settings`,i).then(u).then(m),updateUserTrackingSettings:e=>g(`${n}jetpack/v4/tracking/settings`,a,{body:JSON.stringify(e)}).then(u).then(m),disconnectSite:()=>g(`${n}jetpack/v4/connection`,a,{body:JSON.stringify({isActive:!1})}).then(u).then(m),fetchConnectUrl:()=>p(`${n}jetpack/v4/connection/url`,i).then(u).then(m),unlinkUser:(e=!1,t={})=>{const o={linked:!1,force:!!e};return t.disconnectAllUsers&&(o["disconnect-all-users"]=!0),g(`${n}jetpack/v4/connection/user`,a,{body:JSON.stringify(o)}).then(u).then(m)},reconnect:()=>g(`${n}jetpack/v4/connection/reconnect`,a).then(u).then(m),fetchConnectedPlugins:()=>p(`${n}jetpack/v4/connection/plugins`,i).then(u).then(m),setHasSeenWCConnectionModal:()=>g(`${n}jetpack/v4/seen-wc-connection-modal`,a).then(u).then(m),fetchModules:()=>p(`${n}jetpack/v4/module/all`,i).then(u).then(m),fetchModule:e=>p(`${n}jetpack/v4/module/${e}`,i).then(u).then(m),activateModule:e=>g(`${n}jetpack/v4/module/${e}/active`,a,{body:JSON.stringify({active:!0})}).then(u).then(m),deactivateModule:e=>g(`${n}jetpack/v4/module/${e}/active`,a,{body:JSON.stringify({active:!1})}),updateModuleOptions:(e,t)=>g(`${n}jetpack/v4/module/${e}`,a,{body:JSON.stringify(t)}).then(u).then(m),updateSettings:e=>g(`${n}jetpack/v4/settings`,a,{body:JSON.stringify(e)}).then(u).then(m),getProtectCount:()=>p(`${n}jetpack/v4/module/protect/data`,i).then(u).then(m),resetOptions:e=>g(`${n}jetpack/v4/options/${e}`,a,{body:JSON.stringify({reset:!0})}).then(u).then(m),activateVaultPress:()=>g(`${n}jetpack/v4/plugins`,a,{body:JSON.stringify({slug:"vaultpress",status:"active"})}).then(u).then(m),getVaultPressData:()=>p(`${n}jetpack/v4/module/vaultpress/data`,i).then(u).then(m),installPlugin:(e,t)=>{const o={slug:e,status:"active"};return t&&(o.source=t),g(`${n}jetpack/v4/plugins`,a,{body:JSON.stringify(o)}).then(u).then(m)},activateAkismet:()=>g(`${n}jetpack/v4/plugins`,a,{body:JSON.stringify({slug:"akismet",status:"active"})}).then(u).then(m),getAkismetData:()=>p(`${n}jetpack/v4/module/akismet/data`,i).then(u).then(m),checkAkismetKey:()=>p(`${n}jetpack/v4/module/akismet/key/check`,i).then(u).then(m),checkAkismetKeyTyped:e=>g(`${n}jetpack/v4/module/akismet/key/check`,a,{body:JSON.stringify({api_key:e})}).then(u).then(m),getFeatureTypeStatus:e=>p(`${n}jetpack/v4/feature/${e}`,i).then(u).then(m),fetchStatsData:e=>p(function(e){let t=`${n}jetpack/v4/module/stats/data`;-1!==t.indexOf("?")?t+=`&range=${encodeURIComponent(e)}`:t+=`?range=${encodeURIComponent(e)}`;return t}(e),i).then(u).then(m).then(_),getPluginUpdates:()=>p(`${n}jetpack/v4/updates/plugins`,i).then(u).then(m),getPlans:()=>p(`${n}jetpack/v4/plans`,i).then(u).then(m),fetchSettings:()=>p(`${n}jetpack/v4/settings`,i).then(u).then(m),updateSetting:e=>g(`${n}jetpack/v4/settings`,a,{body:JSON.stringify(e)}).then(u).then(m),fetchSiteData:()=>p(`${n}jetpack/v4/site`,i).then(u).then(m).then((e=>JSON.parse(e.data))),fetchSiteFeatures:()=>p(`${n}jetpack/v4/site/features`,i).then(u).then(m).then((e=>JSON.parse(e.data))),fetchSiteProducts:()=>p(`${n}jetpack/v4/site/products`,i).then(u).then(m),fetchSitePurchases:()=>p(`${n}jetpack/v4/site/purchases`,i).then(u).then(m).then((e=>JSON.parse(e.data))),fetchSiteBenefits:()=>p(`${n}jetpack/v4/site/benefits`,i).then(u).then(m).then((e=>JSON.parse(e.data))),fetchSiteDiscount:()=>p(`${n}jetpack/v4/site/discount`,i).then(u).then(m).then((e=>e.data)),fetchSetupQuestionnaire:()=>p(`${n}jetpack/v4/setup/questionnaire`,i).then(u).then(m),fetchRecommendationsData:()=>p(`${n}jetpack/v4/recommendations/data`,i).then(u).then(m),fetchRecommendationsProductSuggestions:()=>p(`${n}jetpack/v4/recommendations/product-suggestions`,i).then(u).then(m),fetchRecommendationsUpsell:()=>p(`${n}jetpack/v4/recommendations/upsell`,i).then(u).then(m),fetchRecommendationsConditional:()=>p(`${n}jetpack/v4/recommendations/conditional`,i).then(u).then(m),saveRecommendationsData:e=>g(`${n}jetpack/v4/recommendations/data`,a,{body:JSON.stringify({data:e})}).then(u),fetchProducts:()=>p(`${n}jetpack/v4/products`,i).then(u).then(m),fetchRewindStatus:()=>p(`${n}jetpack/v4/rewind`,i).then(u).then(m).then((e=>JSON.parse(e.data))),fetchScanStatus:()=>p(`${n}jetpack/v4/scan`,i).then(u).then(m).then((e=>JSON.parse(e.data))),dismissJetpackNotice:e=>g(`${n}jetpack/v4/notice/${e}`,a,{body:JSON.stringify({dismissed:!0})}).then(u).then(m),fetchPluginsData:()=>p(`${n}jetpack/v4/plugins`,i).then(u).then(m),fetchIntroOffers:()=>p(`${n}jetpack/v4/intro-offers`,i).then(u).then(m),fetchVerifySiteGoogleStatus:e=>p(null!==e?`${n}jetpack/v4/verify-site/google/${e}`:`${n}jetpack/v4/verify-site/google`,i).then(u).then(m),verifySiteGoogle:e=>g(`${n}jetpack/v4/verify-site/google`,a,{body:JSON.stringify({keyring_id:e})}).then(u).then(m),submitSurvey:e=>g(`${n}jetpack/v4/marketing/survey`,a,{body:JSON.stringify(e)}).then(u).then(m),saveSetupQuestionnaire:e=>g(`${n}jetpack/v4/setup/questionnaire`,a,{body:JSON.stringify(e)}).then(u).then(m),updateLicensingError:e=>g(`${n}jetpack/v4/licensing/error`,a,{body:JSON.stringify(e)}).then(u).then(m),updateLicenseKey:e=>g(`${n}jetpack/v4/licensing/set-license`,a,{body:JSON.stringify({license:e})}).then(u).then(m),getUserLicensesCounts:()=>p(`${n}jetpack/v4/licensing/user/counts`,i).then(u).then(m),getUserLicenses:()=>p(`${n}jetpack/v4/licensing/user/licenses`,i).then(u).then(m),updateLicensingActivationNoticeDismiss:e=>g(`${n}jetpack/v4/licensing/user/activation-notice-dismiss`,a,{body:JSON.stringify({last_detached_count:e})}).then(u).then(m),updateRecommendationsStep:e=>g(`${n}jetpack/v4/recommendations/step`,a,{body:JSON.stringify({step:e})}).then(u),confirmIDCSafeMode:()=>g(`${n}jetpack/v4/identity-crisis/confirm-safe-mode`,a).then(u),startIDCFresh:e=>g(`${n}jetpack/v4/identity-crisis/start-fresh`,a,{body:JSON.stringify({redirect_uri:e})}).then(u).then(m),migrateIDC:()=>g(`${n}jetpack/v4/identity-crisis/migrate`,a).then(u),attachLicenses:e=>g(`${n}jetpack/v4/licensing/attach-licenses`,a,{body:JSON.stringify({licenses:e})}).then(u).then(m),fetchSearchPlanInfo:()=>p(`${r}jetpack/v4/search/plan`,i).then(u).then(m),fetchSearchSettings:()=>p(`${r}jetpack/v4/search/settings`,i).then(u).then(m),updateSearchSettings:e=>g(`${r}jetpack/v4/search/settings`,a,{body:JSON.stringify(e)}).then(u).then(m),fetchSearchStats:()=>p(`${r}jetpack/v4/search/stats`,i).then(u).then(m),fetchWafSettings:()=>p(`${n}jetpack/v4/waf`,i).then(u).then(m),updateWafSettings:e=>g(`${n}jetpack/v4/waf`,a,{body:JSON.stringify(e)}).then(u).then(m),fetchWordAdsSettings:()=>p(`${n}jetpack/v4/wordads/settings`,i).then(u).then(m),updateWordAdsSettings:e=>g(`${n}jetpack/v4/wordads/settings`,a,{body:JSON.stringify(e)}),fetchSearchPricing:()=>p(`${r}jetpack/v4/search/pricing`,i).then(u).then(m),fetchMigrationStatus:()=>p(`${n}jetpack/v4/migration/status`,i).then(u).then(m),fetchBackupUndoEvent:()=>p(`${n}jetpack/v4/site/backup/undo-event`,i).then(u).then(m),fetchBackupPreflightStatus:()=>p(`${n}jetpack/v4/site/backup/preflight`,i).then(u).then(m)};function p(e,t){return fetch(l(e),t)}function g(e,t,n){return fetch(e,Object.assign({},t,n)).catch(h)}function _(e){return e.general&&void 0===e.general.response||e.week&&void 0===e.week.response||e.month&&void 0===e.month.response?e:{}}Object.assign(this,d)};function u(e){return e.status>=200&&e.status<300?e:404===e.status?new Promise((()=>{throw e.redirected?new l(e.redirected):new a})):e.json().catch((e=>g(e))).then((t=>{const n=new Error(`${t.message} (Status ${e.status})`);throw n.response=t,n.name="ApiError",n}))}function m(e){return e.json().catch((t=>g(t,e.redirected,e.url)))}function g(e,t,n){throw t?new i(n):new s}function h(){throw new d}},8089:(e,t,n)=>{"use strict";n.d(t,{A:()=>m});var o=n(6427),c=n(7723),r=n(2231),s=n(3619),i=n.n(s),a=n(1609),l=n.n(a),d=n(1112),p=n(7689);const __=c.__,u=e=>{const{label:t,onClick:n,isLoading:c=!1,loadingText:s,isDisabled:i,displayError:a=!1,errorMessage:u=__("An error occurred. Please try again.","jetpack-connection"),variant:m="primary",isExternalLink:g=!1,customClass:h}=e,_=s||l().createElement(o.Spinner,null);return l().createElement(l().Fragment,null,l().createElement(d.A,{className:(0,r.A)(p.A.button,"jp-action-button--button",h),label:t,onClick:n,variant:g?"link":m,isExternalLink:g,disabled:c||i},c?_:t),a&&l().createElement("p",{className:(0,r.A)(p.A.error,"jp-action-button__error")},u))};u.propTypes={label:i().string.isRequired,onClick:i().func,isLoading:i().bool,isDisabled:i().bool,displayError:i().bool,errorMessage:i().oneOfType([i().string,i().element]),variant:i().arrayOf(i().oneOf(["primary","secondary","link"])),isExternalLink:i().bool};const m=u},1112:(e,t,n)=>{"use strict";n.d(t,{A:()=>g});var o=n(8579),c=n.n(o),r=n(6427),s=n(7723),i=n(7750),a=n(8391),l=n(2231),d=n(1609),p=n.n(d),u=n(2258);const __=s.__,m=(0,d.forwardRef)(((e,t)=>{const{children:n,variant:o="primary",size:s="normal",weight:d="bold",icon:m,iconSize:g,disabled:h,isDestructive:_,isLoading:f,isExternalLink:y,className:b,text:k,fullWidth:v,...C}=e,E=(0,l.A)(u.A.button,b,{[u.A.normal]:"normal"===s,[u.A.small]:"small"===s,[u.A.icon]:Boolean(m),[u.A.loading]:f,[u.A.regular]:"regular"===d,[u.A["full-width"]]:v,[u.A["is-icon-button"]]:Boolean(m)&&!n});C.ref=t;const j="normal"===s?20:16,w=y&&p().createElement(p().Fragment,null,p().createElement(i.A,{size:j,icon:a.A,className:u.A["external-icon"]}),p().createElement(r.VisuallyHidden,{as:"span"},/* translators: accessibility text */
__("(opens in a new tab)","jetpack-connection"))),A=y?"_blank":void 0,N=n?.[0]&&null!==n[0]&&"components-tooltip"!==n?.[0]?.props?.className;return p().createElement(r.Button,c()({target:A,variant:o,className:(0,l.A)(E,{"has-text":!!m&&N}),icon:y?void 0:m,iconSize:g,disabled:h,"aria-disabled":h,isDestructive:_,text:k},C),f&&p().createElement(r.Spinner,null),p().createElement("span",null,n),w)}));m.displayName="Button";const g=m},9121:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});n(9535);const o=({format:e="horizontal",icon:t,imageUrl:n})=>React.createElement("div",{className:"jp-components__decorative-card "+(e?"jp-components__decorative-card--"+e:"")},React.createElement("div",{className:"jp-components__decorative-card__image",style:{backgroundImage:n?`url( ${n} )`:""}}),React.createElement("div",{className:"jp-components__decorative-card__content"},React.createElement("div",{className:"jp-components__decorative-card__lines"})),t?React.createElement("div",{className:"jp-components__decorative-card__icon-container"},React.createElement("span",{className:"jp-components__decorative-card__icon jp-components__decorative-card__icon--"+t})):null)},7142:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var o=n(8579),c=n.n(o),r=n(7723),s=n(2231),i=n(1609),a=n.n(i);const __=r.__,l=({logoColor:e="#069e08",showText:t=!0,className:n,height:o=32,...r})=>{const i=t?"0 0 118 32":"0 0 32 32";return a().createElement("svg",c()({xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:i,className:(0,s.A)("jetpack-logo",n),"aria-labelledby":"jetpack-logo-title",height:o},r,{role:"img"}),a().createElement("title",{id:"jetpack-logo-title"},__("Jetpack Logo","jetpack-connection")),a().createElement("path",{fill:e,d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z M15,19H7l8-16V19z M17,29V13h8L17,29z"}),t&&a().createElement(a().Fragment,null,a().createElement("path",{d:"M41.3,26.6c-0.5-0.7-0.9-1.4-1.3-2.1c2.3-1.4,3-2.5,3-4.6V8h-3V6h6v13.4C46,22.8,45,24.8,41.3,26.6z"}),a().createElement("path",{d:"M65,18.4c0,1.1,0.8,1.3,1.4,1.3c0.5,0,2-0.2,2.6-0.4v2.1c-0.9,0.3-2.5,0.5-3.7,0.5c-1.5,0-3.2-0.5-3.2-3.1V12H60v-2h2.1V7.1 H65V10h4v2h-4V18.4z"}),a().createElement("path",{d:"M71,10h3v1.3c1.1-0.8,1.9-1.3,3.3-1.3c2.5,0,4.5,1.8,4.5,5.6s-2.2,6.3-5.8,6.3c-0.9,0-1.3-0.1-2-0.3V28h-3V10z M76.5,12.3 c-0.8,0-1.6,0.4-2.5,1.2v5.9c0.6,0.1,0.9,0.2,1.8,0.2c2,0,3.2-1.3,3.2-3.9C79,13.4,78.1,12.3,76.5,12.3z"}),a().createElement("path",{d:"M93,22h-3v-1.5c-0.9,0.7-1.9,1.5-3.5,1.5c-1.5,0-3.1-1.1-3.1-3.2c0-2.9,2.5-3.4,4.2-3.7l2.4-0.3v-0.3c0-1.5-0.5-2.3-2-2.3 c-0.7,0-2.3,0.5-3.7,1.1L84,11c1.2-0.4,3-1,4.4-1c2.7,0,4.6,1.4,4.6,4.7L93,22z M90,16.4l-2.2,0.4c-0.7,0.1-1.4,0.5-1.4,1.6 c0,0.9,0.5,1.4,1.3,1.4s1.5-0.5,2.3-1V16.4z"}),a().createElement("path",{d:"M104.5,21.3c-1.1,0.4-2.2,0.6-3.5,0.6c-4.2,0-5.9-2.4-5.9-5.9c0-3.7,2.3-6,6.1-6c1.4,0,2.3,0.2,3.2,0.5V13 c-0.8-0.3-2-0.6-3.2-0.6c-1.7,0-3.2,0.9-3.2,3.6c0,2.9,1.5,3.8,3.3,3.8c0.9,0,1.9-0.2,3.2-0.7V21.3z"}),a().createElement("path",{d:"M110,15.2c0.2-0.3,0.2-0.8,3.8-5.2h3.7l-4.6,5.7l5,6.3h-3.7l-4.2-5.8V22h-3V6h3V15.2z"}),a().createElement("path",{d:"M58.5,21.3c-1.5,0.5-2.7,0.6-4.2,0.6c-3.6,0-5.8-1.8-5.8-6c0-3.1,1.9-5.9,5.5-5.9s4.9,2.5,4.9,4.9c0,0.8,0,1.5-0.1,2h-7.3 c0.1,2.5,1.5,2.8,3.6,2.8c1.1,0,2.2-0.3,3.4-0.7C58.5,19,58.5,21.3,58.5,21.3z M56,15c0-1.4-0.5-2.9-2-2.9c-1.4,0-2.3,1.3-2.4,2.9 C51.6,15,56,15,56,15z"})))}},442:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var o=n(9491),c=n(8403);const r=["sm","md","lg"],s=(e,t)=>{const n=Array.isArray(e)?e:[e],s=Array.isArray(t)?t:[t],[i,a,l]=r,d={sm:(0,o.useMediaQuery)(c.A[i]),md:(0,o.useMediaQuery)(c.A[a]),lg:(0,o.useMediaQuery)(c.A[l])};return n.map(((e,t)=>{const n=s[t];return n?((e,t,n)=>{const o=r.indexOf(e),c=o+1,s=t.includes("=");let i=[];return t.startsWith("<")&&(i=r.slice(0,s?c:o)),t.startsWith(">")&&(i=r.slice(s?o:c)),i?.length?i.some((e=>n[e])):n[e]})(e,n,d):d[e]}))}},1876:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var o=n(2231),c=n(1609),r=n.n(c),s=n(6406);const i=({children:e=null,width:t=null,height:n=null,className:c=""})=>r().createElement("div",{className:(0,o.A)(s.A.placeholder,c),style:{width:t,height:n}},e)},9957:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var o=n(4268),c=n(6427),r=n(7723),s=n(1876),i=n(5879);n(4319);const __=r.__,a=e=>-1===e.fraction.indexOf("00"),l=({currencyCode:e="USD",priceDetails:t=__("/month, paid yearly","jetpack-connection"),...n})=>{const l=(0,o.vA)(n.priceBefore,e),d=(0,o.vA)(n.priceAfter,e);return React.createElement("div",{className:"jp-components__pricing-card"},n.icon&&React.createElement("div",{className:"jp-components__pricing-card__icon"},"string"==typeof n.icon?React.createElement("img",{src:n.icon,alt:(0,r.sprintf)(/* translators: placeholder is a product name */
__("Icon for the product %s","jetpack-connection"),n.title)}):n.icon),React.createElement("h1",{className:"jp-components__pricing-card__title"},n.title),React.createElement("div",{className:"jp-components__pricing-card__pricing"},0===n.priceAfter&&React.createElement(s.A,{width:"100%",height:48}),n.priceBefore!==n.priceAfter&&n.priceAfter>0&&React.createElement("div",{className:"jp-components__pricing-card__price-before"},React.createElement("span",{className:"jp-components__pricing-card__currency"},l.symbol),React.createElement("span",{className:"jp-components__pricing-card__price"},l.integer),a(l)&&React.createElement("span",{className:"jp-components__pricing-card__price-decimal"}," ",l.fraction),React.createElement("div",{className:"jp-components__pricing-card__price-strikethrough"})),n.priceAfter>0&&React.createElement(React.Fragment,null,React.createElement("div",{className:"jp-components__pricing-card__price-after"},React.createElement("span",{className:"jp-components__pricing-card__currency"},d.symbol),React.createElement("span",{className:"jp-components__pricing-card__price"},d.integer),a(d)&&React.createElement("span",{className:"jp-components__pricing-card__price-decimal"},d.fraction)),React.createElement("span",{className:"jp-components__pricing-card__price-details"},t))),n.children&&React.createElement("div",{className:"jp-components__pricing-card__extra-content-wrapper"},n.children),n.tosText&&React.createElement("div",{className:"jp-components__pricing-card__tos"},n.tosText),n.ctaText&&React.createElement(React.Fragment,null,!n.tosText&&React.createElement("div",{className:"jp-components__pricing-card__tos"},React.createElement(i.A,{agreeButtonLabel:n.ctaText})),React.createElement("div",{className:"jp-components__pricing-card__cta"},React.createElement(c.Button,{className:"jp-components__pricing-card__button",label:n.ctaText,onClick:n.onCtaClick},n.ctaText))),n.infoText&&React.createElement("div",{className:"jp-components__pricing-card__info"},n.infoText))}},6461:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var o=n(3619),c=n.n(o),r=n(1609),s=n.n(r);n(8325);const i=({color:e="#FFFFFF",className:t="",size:n=20})=>{const o=t+" jp-components-spinner",c={width:n,height:n,fontSize:n,borderTopColor:e},r={borderTopColor:e,borderRightColor:e};return s().createElement("div",{className:o},s().createElement("div",{className:"jp-components-spinner__outer",style:c},s().createElement("div",{className:"jp-components-spinner__inner",style:r})))};i.propTypes={color:c().string,className:c().string,size:c().number};const a=i},5879:(e,t,n)=>{"use strict";n.d(t,{A:()=>h});var o=n(8579),c=n.n(o),r=n(6427),s=n(6087),i=n(7723),a=n(2231),l=n(3924),d=n(7425);n(9634);const __=i.__,p=({multipleButtonsLabels:e})=>Array.isArray(e)&&e.length>1?(0,s.createInterpolateElement)((0,i.sprintf)(/* translators: %1$s is button label 1 and %2$s is button label 2 */
__("By clicking <strong>%1$s</strong> or <strong>%2$s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-connection"),e[0],e[1]),{strong:React.createElement("strong",null),tosLink:React.createElement(g,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(g,{slug:"jetpack-support-what-data-does-jetpack-sync"})}):(0,s.createInterpolateElement)(__("By clicking the buttons above, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-connection"),{tosLink:React.createElement(g,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(g,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),u=({agreeButtonLabel:e})=>(0,s.createInterpolateElement)((0,i.sprintf)(/* translators: %s is a button label */
__("By clicking <strong>%s</strong>, you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site‘s data</shareDetailsLink> with us.","jetpack-connection"),e),{strong:React.createElement("strong",null),tosLink:React.createElement(g,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(g,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),m=()=>(0,s.createInterpolateElement)(__("By continuing you agree to our <tosLink>Terms of Service</tosLink> and to <shareDetailsLink>sync your site’s data</shareDetailsLink> with us. We’ll check if that email is linked to an existing WordPress.com account or create a new one instantly.","jetpack-connection"),{tosLink:React.createElement(g,{slug:"wpcom-tos"}),shareDetailsLink:React.createElement(g,{slug:"jetpack-support-what-data-does-jetpack-sync"})}),g=({slug:e,children:t})=>React.createElement(r.ExternalLink,{className:"terms-of-service__link",href:(0,l.A)(e)},t),h=({className:e,multipleButtons:t,agreeButtonLabel:n,isTextOnly:o,...r})=>React.createElement(d.Ay,c()({className:(0,a.A)(e,"terms-of-service")},r),o?React.createElement(m,null):t?React.createElement(p,{multipleButtonsLabels:t}):React.createElement(u,{agreeButtonLabel:n}))},110:(e,t,n)=>{"use strict";n.d(t,{Q:()=>o,Z:()=>c});const o={"headline-medium":"h1","headline-small":"h2","headline-small-regular":"h2","title-medium":"h3","title-medium-semi-bold":"h3","title-small":"h4",body:"p","body-small":"p","body-extra-small":"p","body-extra-small-bold":"p",label:"p"},c=["mt","mr","mb","ml","mx","my","m","pt","pr","pb","pl","px","py","p"]},7425:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>p});var o=n(8579),c=n.n(o),r=n(2231),s=n(1609),i=n.n(s),a=n(110),l=n(4495);const d=(0,s.forwardRef)((({variant:e="body",children:t,component:n,className:o,...d},p)=>{const u=n||a.Q[e]||"span",m=(0,s.useMemo)((()=>a.Z.reduce(((e,t)=>(void 0!==d[t]&&(e+=l.A[`${t}-${d[t]}`]+" ",delete d[t]),e)),"")),[d]);return i().createElement(u,c()({className:(0,r.A)(l.A.reset,l.A[e],o,m)},d,{ref:p}),t)}));d.displayName="Text";const p=d},3924:(e,t,n)=>{"use strict";function o(e,t={}){const n={};let o;if("undefined"!=typeof window&&(o=window?.JP_CONNECTION_INITIAL_STATE?.calypsoEnv),0===e.search("https://")){const t=new URL(e);e=`https://${t.host}${t.pathname}`,n.url=encodeURIComponent(e)}else n.source=encodeURIComponent(e);for(const e in t)n[e]=encodeURIComponent(t[e]);!Object.keys(n).includes("site")&&"undefined"!=typeof jetpack_redirects&&Object.hasOwn(jetpack_redirects,"currentSiteRawUrl")&&(n.site=jetpack_redirects.currentBlogID??jetpack_redirects.currentSiteRawUrl),o&&(n.calypso_env=o);return"https://jetpack.com/redirect/?"+Object.keys(n).map((e=>e+"="+n[e])).join("&")}n.d(t,{A:()=>o})},6439:(e,t,n)=>{let o={};try{o=n(9074)}catch{console.error("jetpackConfig is missing in your webpack config file. See @automattic/jetpack-config"),o={missingConfig:!0}}const c=e=>Object.hasOwn(o,e);e.exports={jetpackConfigHas:c,jetpackConfigGet:e=>{if(!c(e))throw'This app requires the "'+e+'" Jetpack Config to be defined in your webpack configuration file. See details in @automattic/jetpack-config package docs.';return o[e]}}},8421:(e,t,n)=>{"use strict";n.d(t,{A:()=>p});var o=n(8089),c=n(7723),r=n(3619),s=n.n(r),i=n(1609),a=n.n(i),l=n(9660);const __=c.__,d=e=>{const{apiRoot:t,apiNonce:n,connectLabel:c=__("Connect","jetpack-connection"),registrationNonce:r,redirectUri:s=null,from:i,autoTrigger:d=!1}=e,{handleRegisterSite:p,isRegistered:u,isUserConnected:m,siteIsRegistering:g,userIsConnecting:h,registrationError:_}=(0,l.A)({registrationNonce:r,redirectUri:s,apiRoot:t,apiNonce:n,autoTrigger:d,from:i});return a().createElement(a().Fragment,null,(!u||!m)&&a().createElement(o.A,{label:c,onClick:p,displayError:!!_,isLoading:g||h}))};d.propTypes={connectLabel:s().string,apiRoot:s().string.isRequired,apiNonce:s().string.isRequired,from:s().string,redirectUri:s().string.isRequired,registrationNonce:s().string.isRequired,autoTrigger:s().bool};const p=d},6212:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var o=n(7723),c=n(1609),r=n.n(c),s=n(9660),i=n(5582);const __=o.__,a=({title:e,buttonLabel:t,loadingLabel:n,apiRoot:o,apiNonce:c,registrationNonce:a,from:l,redirectUri:d,images:p,children:u,assetBaseUrl:m,autoTrigger:g,footer:h,skipUserConnection:_,skipPricingPage:f,logo:y})=>{const{handleRegisterSite:b,siteIsRegistering:k,userIsConnecting:v,registrationError:C,isOfflineMode:E}=(0,s.A)({registrationNonce:a,redirectUri:d,apiRoot:o,apiNonce:c,autoTrigger:g,from:l,skipUserConnection:_,skipPricingPage:f}),j=Boolean(C),w=k||v,A=C?.response?.code;return r().createElement(i.A,{title:e||__("Over 5 million WordPress sites are faster and more secure","jetpack-connection"),images:p||[],assetBaseUrl:m,buttonLabel:t||__("Set up Jetpack","jetpack-connection"),loadingLabel:n,handleButtonClick:b,displayButtonError:j,errorCode:A,buttonIsLoading:w,footer:h,isOfflineMode:E,logo:y},u)}},5582:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var o=n(3924),c=n(5879),r=n(8089),s=n(6087),i=n(7723),a=n(1609),l=n.n(a),d=n(2668);n(1772);const __=i.__,p=(e,t)=>{switch(e){case"fail_domain_forbidden":case"fail_ip_forbidden":case"fail_domain_tld":case"fail_subdomain_wpcom":case"siteurl_private_ip":return __("Your site host is on a private network. Jetpack can only connect to public sites.","jetpack-connection");case"connection_disabled":return __("This site has been suspended.","jetpack-connection")}if(t)return(0,s.createInterpolateElement)(__("Unavailable in <a>Offline Mode</a>","jetpack-connection"),{a:l().createElement("a",{href:(0,o.A)("jetpack-support-development-mode"),target:"_blank",rel:"noopener noreferrer"})})},u=({title:e,images:t,children:n,assetBaseUrl:o,isLoading:s,buttonLabel:i,handleButtonClick:a,displayButtonError:u,errorCode:m,buttonIsLoading:g,loadingLabel:h,footer:_,isOfflineMode:f,logo:y})=>l().createElement(d.A,{title:e,assetBaseUrl:o,images:t,className:"jp-connection__connect-screen"+(s?" jp-connection__connect-screen__loading":""),logo:y},l().createElement("div",{className:"jp-connection__connect-screen__content"},n,l().createElement("div",{className:"jp-connection__connect-screen__tos"},l().createElement(c.A,{agreeButtonLabel:i})),l().createElement(r.A,{label:i,onClick:a,displayError:u||f,errorMessage:p(m,f),isLoading:g,isDisabled:f}),l().createElement("span",{className:"jp-connection__connect-screen__loading-message",role:"status"},g?h||__("Loading","jetpack-connection"):""),_&&l().createElement("div",{className:"jp-connection__connect-screen__footer"},_)))},5745:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var o=n(1609),c=n.n(o);const r=({images:e,assetBaseUrl:t=""})=>{if(!e?.length)return null;const n=e.map(((e,n)=>c().createElement(c().Fragment,{key:n},c().createElement("img",{src:t+e,alt:""}))));return c().createElement("div",{className:"jp-connection__connect-screen__image-slider"},n)}},2668:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var o=n(7142),c=n(2231),r=n(1609),s=n.n(r),i=n(5745);n(9064);const a=({title:e,children:t,className:n,assetBaseUrl:r,images:a,logo:l,rna:d=!1})=>{const p=a?.length;return s().createElement("div",{className:(0,c.A)("jp-connection__connect-screen-layout",p?"jp-connection__connect-screen-layout__two-columns":"",n?" "+n:"")},d&&s().createElement("div",{className:"jp-connection__connect-screen-layout__color-blobs"},s().createElement("div",{className:"jp-connection__connect-screen-layout__color-blobs__green"}),s().createElement("div",{className:"jp-connection__connect-screen-layout__color-blobs__yellow"}),s().createElement("div",{className:"jp-connection__connect-screen-layout__color-blobs__blue"})),s().createElement("div",{className:"jp-connection__connect-screen-layout__left"},l||s().createElement(o.A,null),s().createElement("h2",null,e),t),p?s().createElement("div",{className:"jp-connection__connect-screen-layout__right"},s().createElement(i.A,{images:a,assetBaseUrl:r})):null)}},7945:(e,t,n)=>{"use strict";n.d(t,{A:()=>u});var o=n(7723),c=n(3619),r=n.n(c),s=n(1609),i=n.n(s),a=n(2558),l=n(9660),d=n(401);const __=o.__,p=e=>{const{title:t=__("Over 5 million WordPress sites are faster and more secure","jetpack-connection"),autoTrigger:n=!1,buttonLabel:o=__("Set up Jetpack","jetpack-connection"),apiRoot:c,apiNonce:r,registrationNonce:s,from:p,redirectUri:u,children:m,priceBefore:g,priceAfter:h,pricingIcon:_,pricingTitle:f,pricingCurrencyCode:y="USD",wpcomProductSlug:b,siteProductAvailabilityHandler:k,logo:v,rna:C=!1}=e,{handleRegisterSite:E,siteIsRegistering:j,userIsConnecting:w,registrationError:A,isOfflineMode:N}=(0,l.A)({registrationNonce:s,redirectUri:u,apiRoot:c,apiNonce:r,autoTrigger:n,from:p}),S=b||"",{run:R,hasCheckoutStarted:T}=(0,a.A)({productSlug:S,redirectUrl:u,siteProductAvailabilityHandler:k,from:p}),O=Boolean(A),L=j||w||T,I=S?R:E;return i().createElement(d.A,{title:t,buttonLabel:o,priceBefore:g,priceAfter:h,pricingIcon:_,pricingTitle:f,pricingCurrencyCode:y,handleButtonClick:I,displayButtonError:O,buttonIsLoading:L,logo:v,isOfflineMode:N,rna:C},m)};p.propTypes={title:r().string,buttonLabel:r().string,apiRoot:r().string.isRequired,apiNonce:r().string.isRequired,registrationNonce:r().string.isRequired,from:r().string,redirectUri:r().string.isRequired,autoTrigger:r().bool,pricingTitle:r().string.isRequired,pricingIcon:r().oneOfType([r().string,r().element]),priceBefore:r().number.isRequired,priceAfter:r().number.isRequired,pricingCurrencyCode:r().string,wpcomProductSlug:r().string,checkSiteHasWpcomProduct:r().func,logo:r().element};const u=p},401:(e,t,n)=>{"use strict";n.d(t,{A:()=>b});var o=n(8089),c=n(3924),r=n(9957),s=n(5879),i=n(6087),a=n(7723),l=n(2231),d=n(4804),p=n.n(d),u=n(3619),m=n.n(u),g=n(1609),h=n.n(g),_=n(2668);n(381);const __=a.__,f=p()("jetpack:connection:ConnectScreenRequiredPlanVisual"),y=e=>{const{title:t,buttonLabel:n,children:a,priceBefore:d,priceAfter:p,pricingIcon:u,pricingTitle:m,pricingCurrencyCode:g="USD",isLoading:y=!1,handleButtonClick:b=()=>{},displayButtonError:k=!1,buttonIsLoading:v=!1,logo:C,isOfflineMode:E,rna:j=!1}=e;f("props are %o",e);const w=(0,i.createInterpolateElement)(__("Already have a subscription? <connectButton/>","jetpack-connection"),{connectButton:h().createElement(o.A,{label:__("Log in to get started","jetpack-connection"),onClick:b,isLoading:v})}),A=E?(0,i.createInterpolateElement)(__("Unavailable in <a>Offline Mode</a>","jetpack-connection"),{a:h().createElement("a",{href:(0,c.A)("jetpack-support-development-mode"),target:"_blank",rel:"noopener noreferrer"})}):void 0;return h().createElement(_.A,{title:t,className:(0,l.A)("jp-connection__connect-screen-required-plan",y?"jp-connection__connect-screen-required-plan__loading":"",j?"rna":""),logo:C,rna:j},h().createElement("div",{className:"jp-connection__connect-screen-required-plan__content"},a,h().createElement("div",{className:"jp-connection__connect-screen-required-plan__pricing-card"},h().createElement(r.A,{title:m,icon:u,priceBefore:d,currencyCode:g,priceAfter:p},h().createElement(s.A,{agreeButtonLabel:n}),h().createElement(o.A,{label:n,onClick:b,displayError:k||E,errorMessage:A,isLoading:v,isDisabled:E}))),!E&&h().createElement("div",{className:"jp-connection__connect-screen-required-plan__with-subscription"},w)))};y.propTypes={pricingTitle:m().string.isRequired,priceBefore:m().number.isRequired,priceAfter:m().number.isRequired,pricingCurrencyCode:m().string,title:m().string,buttonLabel:m().string,pricingIcon:m().oneOfType([m().string,m().element]),isLoading:m().bool,handleButtonClick:m().func,displayButtonError:m().bool,buttonIsLoading:m().bool,logo:m().element,isOfflineMode:m().bool};const b=y},7840:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var o=n(5932),c=n(3619),r=n.n(c),s=n(1609);const i=e=>{const{redirectFunc:t=e=>window.location.assign(e),connectUrl:n,redirectUri:c=null,from:r}=e,[i,a]=(0,s.useState)(null);return n&&n!==i&&a(n),(0,s.useEffect)((()=>{i||o.Ay.fetchAuthorizationUrl(c).then((e=>a(e.authorizeUrl))).catch((e=>{throw e}))}),[]),i?(t(i+(r?(i.includes("?")?"&":"?")+"from="+encodeURIComponent(r):"")),null):null};i.propTypes={connectUrl:r().string,redirectUri:r().string.isRequired,from:r().string,redirectFunc:r().func};const a=i},648:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var o=n(7723),c=n(3619),r=n.n(c),s=n(1609),i=n.n(s),a=n(7499);const __=o.__,l=e=>{const{connectedPlugins:t,disconnectingPlugin:n}=e,o=(0,s.useMemo)((()=>{if(t){return Object.keys(t).map((e=>Object.assign({slug:e},t[e]))).filter((e=>n!==e.slug))}return[]}),[t,n]);return t&&o.length>0?i().createElement(i().Fragment,null,i().createElement("div",{className:"jp-connection__disconnect-dialog__step-copy"},i().createElement("p",{className:"jp-connection__disconnect-dialog__large-text"},__("Jetpack is powering other plugins on your site. If you disconnect, these plugins will no longer work.","jetpack-connection"))),i().createElement("div",{className:"jp-connection__disconnect-card__group"},o.map((e=>i().createElement(a.A,{title:e.name,key:e.slug}))))):null};l.propTypes={connectedPlugins:r().array,disconnectingPlugin:r().string};const d=l},7088:(e,t,n)=>{"use strict";n.d(t,{A:()=>m});var o=n(442),c=n(6461),r=n(6427),s=n(7723),i=n(3619),a=n.n(i),l=n(1609),d=n.n(l),p=n(4175);const __=s.__,u=e=>{const{message:t,isRestoringConnection:n,restoreConnectionCallback:i,restoreConnectionError:a}=e,[l]=(0,o.A)(["md"],[">"]),u=p.A.notice+(l?" "+p.A["bigger-than-medium"]:""),m=d().createElement(r.Icon,{icon:d().createElement(r.SVG,{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},d().createElement(r.Path,{d:"M11.7815 4.93772C11.8767 4.76626 12.1233 4.76626 12.2185 4.93772L20.519 19.8786C20.6116 20.0452 20.4911 20.25 20.3005 20.25H3.69951C3.50889 20.25 3.3884 20.0452 3.48098 19.8786L11.7815 4.93772Z",stroke:"#D63638",strokeWidth:"1.5"}),d().createElement(r.Path,{d:"M13 10H11V15H13V10Z",fill:"#D63638"}),d().createElement(r.Path,{d:"M13 16H11V18H13V16Z",fill:"#D63638"}))});if(!t)return null;if(n)return d().createElement(r.Notice,{status:"error",isDismissible:!1,className:u},d().createElement("div",{className:p.A.message},d().createElement(c.A,{color:"#B32D2E",size:24}),__("Reconnecting Jetpack","jetpack-connection")));const g=a?d().createElement(r.Notice,{status:"error",isDismissible:!1,className:u+" "+p.A.error},d().createElement("div",{className:p.A.message},m,(0,s.sprintf)(/* translators: placeholder is the error. */
__("There was an error reconnecting Jetpack. Error: %s","jetpack-connection"),a))):null;return d().createElement(d().Fragment,null,g,d().createElement(r.Notice,{status:"error",isDismissible:!1,className:u},d().createElement("div",{className:p.A.message},m,t),i&&d().createElement("a",{onClick:i,onKeyDown:i,className:p.A.button,href:"#"},__("Restore Connection","jetpack-connection"))))};u.propTypes={message:a().string.isRequired,restoreConnectionCallback:a().func,isRestoringConnection:a().bool,restoreConnectionError:a().string};const m=u},7499:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var o=n(3619),c=n.n(o),r=n(1609),s=n.n(r);n(7419);const i=e=>{const{title:t,value:n,description:o}=e;return s().createElement("div",{className:"jp-connection__disconnect-card card"},s().createElement("div",{className:"jp-connection__disconnect-card__card-content"},s().createElement("p",{className:"jp-connection__disconnect-card__card-headline"},t),(n||o)&&s().createElement("div",{className:"jp-connection__disconnect-card__card-stat-block"},s().createElement("span",{className:"jp-connection__disconnect-card__card-stat"},n),s().createElement("div",{className:"jp-connection__disconnect-card__card-description"},o))))};i.propTypes={title:c().string,value:c().oneOfType([c().string,c().number]),description:c().string};const a=i},3269:(e,t,n)=>{"use strict";n.d(t,{A:()=>f});var o=n(372),c=n(5932),r=n(6439),s=n(6427),i=n(7723),a=n(3619),l=n.n(a),d=n(1609),p=n.n(d),u=(n(785),n(4472)),m=n(8503),g=n(412),h=n(8090);const __=i.__,_=e=>{const[t,n]=(0,d.useState)(!1),[i,a]=(0,d.useState)(!1),[l,_]=(0,d.useState)(!1),[f,y]=(0,d.useState)(!1),[b,k]=(0,d.useState)(!1),[v,C]=(0,d.useState)(!1),{apiRoot:E,apiNonce:j,connectedPlugins:w,title:A=__("Are you sure you want to disconnect?","jetpack-connection"),pluginScreenDisconnectCallback:N,onDisconnected:S,onError:R,disconnectStepComponent:T,context:O="jetpack-dashboard",connectedUser:L={},connectedSiteId:I,isOpen:P,onClose:D}=e;let U="";(0,r.jetpackConfigHas)("consumer_slug")&&(U=(0,r.jetpackConfigGet)("consumer_slug"));const x=(0,d.useMemo)((()=>({context:O,plugin:U})),[O,U]);(0,d.useEffect)((()=>{c.Ay.setApiRoot(E),c.Ay.setApiNonce(j)}),[E,j]),(0,d.useEffect)((()=>{L&&L.ID&&L.login&&o.A.initialize(L.ID,L.login)}),[L,L.ID,L.login]),(0,d.useEffect)((()=>{P&&o.A.tracks.recordEvent("jetpack_disconnect_dialog_open",x)}),[P,x]),(0,d.useEffect)((()=>{P&&(i?!i||f||b?f&&!b?o.A.tracks.recordEvent("jetpack_disconnect_dialog_step",Object.assign({},{step:"survey"},x)):b&&o.A.tracks.recordEvent("jetpack_disconnect_dialog_step",Object.assign({},{step:"thank_you"},x)):o.A.tracks.recordEvent("jetpack_disconnect_dialog_step",Object.assign({},{step:"disconnect_confirm"},x)):o.A.tracks.recordEvent("jetpack_disconnect_dialog_step",Object.assign({},{step:"disconnect"},x)))}),[P,i,f,b,x]);const F=(0,d.useCallback)((()=>{c.Ay.disconnectSite().then((()=>{n(!1),a(!0)})).catch((e=>{n(!1),_(e),R&&R(e)}))}),[n,a,_,R]),M=(0,d.useCallback)(((e,t)=>{C(!0),fetch("https://public-api.wordpress.com/wpcom/v2/marketing/feedback-survey",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(e)}).then((e=>e.json())).then((e=>{if(!0!==e.success)throw new Error("Survey endpoint returned error code "+e.code);o.A.tracks.recordEvent("jetpack_disconnect_survey_submit",t),k(!0),C(!1)})).catch((e=>{o.A.tracks.recordEvent("jetpack_disconnect_survey_error",Object.assign({},{error:e.message},t)),k(!0),C(!1)}))}),[C,k]),B=(0,d.useCallback)((e=>{e&&e.preventDefault(),_(!1),n(!0),"plugins"!==O?F():N&&N(e)}),[_,n,N,O,F]),$=(0,d.useCallback)((e=>o.A.tracks.recordEvent(e,x)),[x]),J=(0,d.useCallback)((()=>!(!L.ID||!I)),[L,I]),z=(0,d.useCallback)(((e,t,n)=>{if(n&&n.preventDefault(),!J())return void k(!0);const o={site_id:I,user_id:L.ID,survey_id:"jetpack-plugin-disconnect",survey_responses:{"why-cancel":{response:e,text:t||null}}},c=Object.assign({},x,{disconnect_reason:e});M(o,c)}),[M,k,J,I,L,x]),q=(0,d.useCallback)((e=>{e&&e.preventDefault(),S&&S(),D()}),[S,D]),G=(0,d.useCallback)((e=>{e&&e.preventDefault(),y(!0)}),[y]);return p().createElement(p().Fragment,null,P&&p().createElement(s.Modal,{title:"",contentLabel:A,aria:{labelledby:"jp-connection__disconnect-dialog__heading"},onRequestClose:D,shouldCloseOnClickOutside:!1,shouldCloseOnEsc:!1,isDismissible:!1,className:"jp-connection__disconnect-dialog"+(i?" jp-connection__disconnect-dialog__success":"")},i?!i||f||b?f&&!b?p().createElement(g.A,{isSubmittingFeedback:v,onFeedBackProvided:z,onExit:q}):b?p().createElement(h.A,{onExit:q}):void 0:p().createElement(m.A,{canProvideFeedback:J(),onProvideFeedback:G,onExit:q}):p().createElement(u.A,{title:A,connectedPlugins:w,disconnectStepComponent:T,isDisconnecting:t,closeModal:D,onDisconnect:B,disconnectError:l,context:O,disconnectingPlugin:U,trackModalClick:$})))};_.propTypes={apiRoot:l().string.isRequired,apiNonce:l().string.isRequired,title:l().string,onDisconnected:l().func,onError:l().func,context:l().string,connectedPlugins:l().oneOfType([l().array,l().object]),pluginScreenDisconnectCallback:l().func,disconnectStepComponent:l().element,connectedUser:l().object,connectedSiteId:l().number,isOpen:l().bool,onClose:l().func};const f=_},8503:(e,t,n)=>{"use strict";n.d(t,{A:()=>m});var o=n(9121),c=n(6427),r=n(6087),s=n(7723),i=n(3619),a=n.n(i),l=n(1609),d=n.n(l),p=n(2365);const __=s.__,u=e=>{const{onExit:t,canProvideFeedback:n,onProvideFeedback:s}=e;return d().createElement("div",{className:"jp-connection__disconnect-dialog__content"},d().createElement(o.A,{icon:"unlink",imageUrl:p}),d().createElement("div",{className:"jp-connection__disconnect-dialog__step-copy jp-connection__disconnect-dialog__step-copy--narrow"},d().createElement("h1",null,(0,r.createInterpolateElement)(__("Jetpack has been <br/>successfully disconnected.","jetpack-connection"),{br:d().createElement("br",null)})),n&&d().createElement(d().Fragment,null,d().createElement("p",null,__("We’re sorry to see you go. Here at Jetpack, we’re always striving to provide the best experience for our customers. Please take our short survey (2 minutes, promise).","jetpack-connection")),d().createElement("p",null,d().createElement(c.Button,{variant:"primary",onClick:s,className:"jp-connection__disconnect-dialog__btn-back-to-wp"},__("Help us improve","jetpack-connection"))),d().createElement("a",{className:"jp-connection__disconnect-dialog__link jp-connection__disconnect-dialog__link--bold",href:"#",onClick:t},__("No thank you","jetpack-connection"))),!n&&d().createElement(d().Fragment,null,d().createElement("p",null,d().createElement(c.Button,{variant:"primary",onClick:t,className:"jp-connection__disconnect-dialog__btn-back-to-wp"},__("Back to my website","jetpack-connection"))))))};u.propTypes={onExit:a().func,onProvideFeedback:a().func,canProvideFeedback:a().bool};const m=u},4472:(e,t,n)=>{"use strict";n.d(t,{A:()=>m});var o=n(3924),c=n(6427),r=n(6087),s=n(7723),i=n(3619),a=n.n(i),l=n(1609),d=n.n(l),p=n(648);const __=s.__,u=e=>{const{title:t,isDisconnecting:n,onDisconnect:s,disconnectError:i,disconnectStepComponent:a,connectedPlugins:u,disconnectingPlugin:m,closeModal:g,context:h,trackModalClick:_}=e,f=(0,l.useCallback)((()=>_("jetpack_disconnect_dialog_click_learn_about")),[_]),y=(0,l.useCallback)((()=>_("jetpack_disconnect_dialog_click_support")),[_]),b=(0,l.useCallback)((()=>{_("jetpack_disconnect_dialog_click_stay_connected"),g()}),[_,g]),k=(0,l.useCallback)((e=>{_("jetpack_disconnect_dialog_click_disconnect"),s(e)}),[_,s]),v=(0,l.useCallback)((e=>{"Escape"!==e.key||n||b()}),[b,n]);(0,l.useEffect)((()=>(document.addEventListener("keydown",v,!1),()=>{document.removeEventListener("keydown",v,!1)})),[]);return d().createElement(d().Fragment,null,d().createElement("div",{className:"jp-connection__disconnect-dialog__content"},d().createElement("h1",{id:"jp-connection__disconnect-dialog__heading"},t),d().createElement(p.A,{connectedPlugins:u,disconnectingPlugin:m}),a,(()=>{if(!(u&&Object.keys(u).filter((e=>e!==m)).length)&&!a)return d().createElement("div",{className:"jp-connection__disconnect-dialog__step-copy"},d().createElement("p",{className:"jp-connection__disconnect-dialog__large-text"},__("Jetpack is currently powering multiple products on your site.","jetpack-connection"),d().createElement("br",null),__("Once you disconnect Jetpack, these will no longer work.","jetpack-connection")))})()),d().createElement("div",{className:"jp-connection__disconnect-dialog__actions"},d().createElement("div",{className:"jp-row"},d().createElement("div",{className:"lg-col-span-8 md-col-span-9 sm-col-span-4"},d().createElement("p",null,(0,r.createInterpolateElement)(__("<strong>Need help?</strong> Learn more about the <jpConnectionInfoLink>Jetpack connection</jpConnectionInfoLink> or <jpSupportLink>contact Jetpack support</jpSupportLink>.","jetpack-connection"),{strong:d().createElement("strong",null),jpConnectionInfoLink:d().createElement(c.ExternalLink,{href:(0,o.A)("why-the-wordpress-com-connection-is-important-for-jetpack"),className:"jp-connection__disconnect-dialog__link",onClick:f}),jpSupportLink:d().createElement(c.ExternalLink,{href:(0,o.A)("jetpack-support"),className:"jp-connection__disconnect-dialog__link",onClick:y})}))),d().createElement("div",{className:"jp-connection__disconnect-dialog__button-wrap lg-col-span-4 md-col-span-7 sm-col-span-4"},d().createElement(c.Button,{variant:"primary",disabled:n,onClick:b,className:"jp-connection__disconnect-dialog__btn-dismiss"},"plugins"===h?__("Cancel","jetpack-connection"):__("Stay connected","jetpack-connection",0)),(()=>{let e=__("Disconnect","jetpack-connection");return n?e=__("Disconnecting…","jetpack-connection"):"plugins"===h&&(e=__("Deactivate","jetpack-connection")),d().createElement(c.Button,{variant:"primary",disabled:n,onClick:k,className:"jp-connection__disconnect-dialog__btn-disconnect"},e)})())),i&&d().createElement("p",{className:"jp-connection__disconnect-dialog__error"},i)))};u.propTypes={title:a().string,isDisconnecting:a().bool,onDisconnect:a().func,disconnectError:a().bool,disconnectStepComponent:a().element,connectedPlugins:a().array,disconnectingPlugin:a().string,closeModal:a().func,context:a().string,trackModalClick:a().func};const m=u},412:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var o=n(7723),c=n(3619),r=n.n(c),s=n(1609),i=n.n(s),a=(n(255),n(2951));const __=o.__,l=e=>{const{onExit:t,onFeedBackProvided:n,isSubmittingFeedback:o}=e;return i().createElement("div",{className:"jp-connection__disconnect-dialog__content"},i().createElement("h1",null,__("Before you go, help us improve Jetpack","jetpack-connection")),i().createElement("p",{className:"jp-connection__disconnect-dialog__large-text"},__("Let us know what didn‘t work for you","jetpack-connection")),i().createElement(a.A,{onSubmit:n,isSubmittingFeedback:o}),i().createElement("a",{className:"jp-connection__disconnect-dialog__link jp-connection__disconnect-dialog__link--bold",href:"#",onClick:t},__("Skip for now","jetpack-connection")))};l.PropTypes={onExit:r().func,onFeedBackProvided:r().func,isSubmittingFeedback:r().bool};const d=l},8090:(e,t,n)=>{"use strict";n.d(t,{A:()=>m});var o=n(9121),c=n(6427),r=n(6087),s=n(7723),i=n(3619),a=n.n(i),l=n(1609),d=n.n(l),p=n(9362);const __=s.__,u=e=>{const{onExit:t}=e;return d().createElement("div",{className:"jp-connection__disconnect-dialog__content"},d().createElement(o.A,{format:"vertical",imageUrl:p}),d().createElement("div",{className:"jp-connection__disconnect-dialog__copy"},d().createElement("h1",null,__("Thank you!","jetpack-connection")),d().createElement("p",{className:"jp-connection__disconnect-dialog__large-text"},(0,r.createInterpolateElement)(__("Your answer has been submitted. <br/>Thanks for your input on how we can improve Jetpack.","jetpack-connection"),{br:d().createElement("br",null)})),d().createElement(c.Button,{variant:"primary",onClick:t,className:"jp-connection__disconnect-dialog__btn-back-to-wp"},__("Back to my website","jetpack-connection"))))};u.PropTypes={onExit:a().func,assetBaseUrl:a().string};const m=u},2951:(e,t,n)=>{"use strict";n.d(t,{A:()=>p});var o=n(6427),c=n(7723),r=n(3619),s=n.n(r),i=n(1609),a=n.n(i),l=n(8233);const __=c.__,d=e=>{const{onSubmit:t,isSubmittingFeedback:n}=e,[c,r]=(0,i.useState)(),[s,d]=(0,i.useState)(),p=[{id:"troubleshooting",answerText:__("Troubleshooting - I'll be reconnecting afterwards.","jetpack-connection")},{id:"not-working",answerText:__("I can't get it to work.","jetpack-connection")},{id:"slowed-down-site",answerText:__("It slowed down my site.","jetpack-connection")},{id:"buggy",answerText:__("It's buggy.","jetpack-connection")},{id:"what-does-it-do",answerText:__("I don't know what it does.","jetpack-connection")}],u="another-reason",m=(0,i.useCallback)((()=>{t(c,c===u?s:"")}),[t,u,s,c]),g=(0,i.useCallback)((e=>{const t=e.target.value;e.stopPropagation(),d(t)}),[d]),h=e=>e===c?"jp-connect__disconnect-survey-card--selected":"",_=(0,i.useCallback)(((e,t)=>{switch(t.key){case"Enter":case"Space":case"Spacebar":case" ":r(e)}}),[r]);return a().createElement(a().Fragment,null,a().createElement("div",{className:"jp-connection__disconnect-dialog__survey"},p.map((e=>a().createElement(l.A,{key:e.id,id:e.id,onClick:r,onKeyDown:_,className:"card jp-connect__disconnect-survey-card "+h(e.id)},a().createElement("p",{className:"jp-connect__disconnect-survey-card__answer"},e.answerText)))),a().createElement(l.A,{id:u,key:u,onClick:r,onKeyDown:_,className:"card jp-connect__disconnect-survey-card "+h(u)},a().createElement("p",{className:"jp-connect__disconnect-survey-card__answer"},__("Other:","jetpack-connection")," ",a().createElement("input",{placeholder:__("share your experience","jetpack-connection"),className:"jp-connect__disconnect-survey-card__input",type:"text",value:s,onChange:g,maxLength:1e3})))),a().createElement("p",null,a().createElement(o.Button,{disabled:!c||n,variant:"primary",onClick:m,className:"jp-connection__disconnect-dialog__btn-back-to-wp"},n?__("Submitting…","jetpack-connection"):__("Submit Feedback","jetpack-connection",0))))};d.PropTypes={onSubmit:s().func,isSubmittingFeedback:s().bool};const p=d},8233:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var o=n(1609),c=n.n(o);n(255);const r=e=>{const{id:t,onClick:n,onKeyDown:r,children:s,className:i}=e,a=(0,o.useCallback)((()=>{n(t)}),[t,n]),l=(0,o.useCallback)((e=>{r(t,e)}),[t,r]);return c().createElement("div",{tabIndex:"0",role:"button",onClick:a,onKeyDown:l,className:"card jp-connect__disconnect-survey-card "+i},s)}},7018:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var o=n(7723),c=n(3619),r=n.n(c),s=n(1609),i=n.n(s);n(3732);const __=o.__,a=e=>{const{title:t,isLoading:n=!1,width:o="100%",displayTOS:c,scrollToIframe:r=!1,connectUrl:a,onComplete:l,onThirdPartyCookiesBlocked:d,location:p}=e;let{height:u="300"}=e;const m=(0,s.useRef)(void 0),g=(0,s.useRef)(void 0),h=e=>{if(g.current&&e.source===g.current.contentWindow)switch(e.data){case"close":window.removeEventListener("message",h),l&&l();break;case"wpcom_nocookie":d&&d()}};(0,s.useEffect)((()=>{r&&window.scrollTo(0,m.current.offsetTop-10),window.addEventListener("message",h)}));let _=a.replace("authorize/","authorize_iframe/");return _.includes("?")||(_+="?"),c&&(_+="&display-tos",u=(parseInt(u)+50).toString()),_+="&iframe_height="+parseInt(u),p&&(_+="&iframe_source="+p),i().createElement("div",{className:"dops-card fade-in jp-iframe-wrap",ref:m},i().createElement("h1",null,t),n?i().createElement("p",null,__("Loading…","jetpack-connection")):i().createElement("iframe",{title:t,width:o,height:u,src:_,ref:g}))};a.propTypes={title:r().string.isRequired,isLoading:r().bool,width:r().string,height:r().string,connectUrl:r().string.isRequired,displayTOS:r().bool.isRequired,scrollToIframe:r().bool,onComplete:r().func,onThirdPartyCookiesBlocked:r().func,location:r().string};const l=a},4981:(e,t,n)=>{"use strict";n.d(t,{A:()=>w});var o=n(372),c=n(5932),r=n(7425),s=n(3924),i=n(1112),a=n(6427),l=n(6087),d=n(7723),p=n(7750),u=n(8391),m=n(1386),g=n(2231),h=n(3619),_=n.n(h),f=n(1609),y=n.n(f),b=n(7088),k=n(3269),v=n(3735);n(2057);const __=d.__,C=e=>{const{title:t=__("Manage your Jetpack connection","jetpack-connection"),apiRoot:n,apiNonce:i,connectedPlugins:l,onDisconnected:d,onUnlinked:p,context:u="jetpack-dashboard",connectedUser:m={},connectedSiteId:g,isOpen:h=!1,onClose:_}=e,[C,w]=(0,f.useState)(!1),[A,N]=(0,f.useState)(!1),[S,R]=(0,f.useState)(""),[T,O]=(0,f.useState)(!1);(0,f.useEffect)((()=>{c.Ay.setApiRoot(n),c.Ay.setApiNonce(i)}),[n,i]);const L=(0,f.useCallback)((e=>{e&&e.preventDefault(),w(!0)}),[w]),I=(0,f.useCallback)((e=>{e&&e.preventDefault(),w(!1)}),[w]),P=(0,f.useMemo)((()=>!!m.currentUser?.permissions?.manage_options),[m.currentUser]),D=(0,f.useCallback)((()=>{m.currentUser?.isConnected&&(N(!0),R(""),c.Ay.unlinkUser(P).then((()=>{N(!1),_(),p()})).catch((()=>{let e=__("There was some trouble disconnecting your user account, your Jetpack plugin(s) may be outdated. Please visit your plugins page and make sure all Jetpack plugins are updated.","jetpack-connection");P||(e=__("There was some trouble disconnecting your user account, your Jetpack plugin(s) may be outdated. Please ask a site admin to update Jetpack","jetpack-connection")),R(e),N(!1)})))}),[N,R,P,p,_,m]),U=(0,f.useCallback)((e=>{e&&e.preventDefault(),m.currentUser?.isMaster?O(!0):(o.A.tracks.recordEvent("jetpack_manage_connection_dialog_disconnect_user_click",{context:u}),D())}),[D,u,m]),x=(0,f.useMemo)((()=>A),[A]),F=__("Disconnecting…","jetpack-connection"),M=(0,f.useCallback)((()=>{O(!1)}),[O]);return y().createElement(y().Fragment,null,h&&y().createElement(y().Fragment,null,y().createElement(a.Modal,{title:"",contentLabel:t,aria:{labelledby:"jp-connection__manage-dialog__heading"},shouldCloseOnClickOutside:!1,shouldCloseOnEsc:!1,isDismissible:!1,className:"jp-connection__manage-dialog"},y().createElement("div",{className:"jp-connection__manage-dialog__content"},y().createElement("h1",{id:"jp-connection__manage-dialog__heading"},t),y().createElement(r.Ay,{className:"jp-connection__manage-dialog__large-text"},__("At least one user must be connected for your Jetpack products to work properly.","jetpack-connection")),P&&m.currentUser?.isConnected&&m.currentUser?.isMaster&&y().createElement(E,{title:__("Transfer ownership to another admin","jetpack-connection"),link:(0,s.A)("calypso-settings-manage-connection",{site:window?.myJetpackInitialState?.siteSuffix}),isExternal:!0,key:"transfer",action:"transfer",disabled:x}),m.currentUser?.isConnected&&y().createElement(y().Fragment,null,""!==S&&y().createElement(b.A,{message:S}),y().createElement(E,{title:A?F:__("Disconnect my user account","jetpack-connection"),onClick:U,key:"unlink",action:"unlink",disabled:x})),P&&y().createElement(E,{title:__("Disconnect Jetpack","jetpack-connection"),onClick:L,key:"disconnect",action:"disconnect",disabled:x})),y().createElement(j,{onClose:_,disabled:x})),y().createElement(k.A,{apiRoot:n,apiNonce:i,onDisconnected:d,connectedPlugins:l,connectedSiteId:g,connectedUser:m,isOpen:C,onClose:I,context:u}),y().createElement(v.A,{isOpen:T,onClose:M,apiRoot:n,apiNonce:i,onDisconnected:d,onUnlinked:p})))},E=({title:e,onClick:t=()=>null,isExternal:n=!1,link:o="#",action:c,disabled:r=!1})=>{const s=(0,f.useCallback)((e=>e.preventDefault()),[]);return y().createElement("div",{className:"jp-connection__manage-dialog__action-card card"+(r?" disabled":"")},y().createElement("div",{className:"jp-connection__manage-dialog__action-card__card-content"},y().createElement("a",{href:o,className:(0,g.A)("jp-connection__manage-dialog__action-card__card-headline",c),onClick:r?s:t,target:n?"_blank":"_self",rel:"noopener noreferrer"},e,y().createElement(p.A,{icon:n?u.A:m.A,className:"jp-connection__manage-dialog__action-card__icon"}))))},j=({onClose:e,disabled:t})=>y().createElement("div",{className:"jp-row jp-connection__manage-dialog__actions"},y().createElement("div",{className:"jp-connection__manage-dialog__text-wrap lg-col-span-9 md-col-span-7 sm-col-span-3"},y().createElement(r.Ay,null,(0,l.createInterpolateElement)(__("<strong>Need help?</strong> Learn more about the <connectionInfoLink>Jetpack connection</connectionInfoLink> or <supportLink>contact Jetpack support</supportLink>","jetpack-connection"),{strong:y().createElement("strong",null),connectionInfoLink:y().createElement(a.ExternalLink,{href:(0,s.A)("why-the-wordpress-com-connection-is-important-for-jetpack"),className:"jp-connection__manage-dialog__link"}),supportLink:y().createElement(a.ExternalLink,{href:(0,s.A)("jetpack-support"),className:"jp-connection__manage-dialog__link"})}))),y().createElement("div",{className:"jp-connection__manage-dialog__button-wrap lg-col-span-3 md-col-span-1 sm-col-span-1"},y().createElement(i.A,{weight:"regular",variant:"secondary",onClick:e,className:"jp-connection__manage-dialog__btn-dismiss",disabled:t},__("Cancel","jetpack-connection"))));C.propTypes={title:_().string,apiRoot:_().string.isRequired,apiNonce:_().string.isRequired,connectedPlugins:_().oneOfType([_().array,_().object]),onDisconnected:_().func,onUnlinked:_().func,context:_().string,connectedUser:_().object,connectedSiteId:_().number,isOpen:_().bool,onClose:_().func};const w=C},3735:(e,t,n)=>{"use strict";n.d(t,{A:()=>b});var o=n(372),c=n(5932),r=n(3924),s=n(6427),i=n(6087),a=n(7723),l=n(7750),d=n(8391),p=n(1386),u=n(2231),m=n(3619),g=n.n(m),h=n(1609),_=n.n(h);n(1127);const __=a.__,f=({isOpen:e,onClose:t,apiRoot:n,apiNonce:a,onDisconnected:l,onUnlinked:d})=>{const[p,u]=(0,h.useState)(!1),[m,g]=(0,h.useState)(""),f=__("Disconnecting…","jetpack-connection"),b=__("Disconnect","jetpack-connection");(0,h.useEffect)((()=>{c.Ay.setApiRoot(n),c.Ay.setApiNonce(a)}),[n,a]);const k=(0,h.useCallback)((()=>{t()}),[t]),v=(0,h.useCallback)((()=>{o.A.tracks.recordEvent("jetpack_manage_connection_dialog_owner_disconnect_click"),u(!0),g(""),c.Ay.unlinkUser(!0,{disconnectAllUsers:!0}).then((()=>{o.A.tracks.recordEvent("jetpack_manage_connection_dialog_owner_disconnect_success"),l&&l(),d&&d()})).catch((()=>{o.A.tracks.recordEvent("jetpack_manage_connection_dialog_owner_disconnect_error"),g(__("There was a problem disconnecting your account. Please try again.","jetpack-connection")),u(!1)}))}),[l,d]);return e&&_().createElement(s.Modal,{title:"",contentLabel:__("Disconnect Owner Account","jetpack-connection"),aria:{labelledby:"jp-connection__disconnect-dialog__heading"},onRequestClose:k,className:"jp-connection__disconnect-dialog"},_().createElement("div",{className:"jp-connection__disconnect-dialog__content"},_().createElement("h1",{id:"jp-connection__disconnect-dialog__heading"},__("Disconnect Owner Account","jetpack-connection")),_().createElement("p",{className:"jp-connection__disconnect-dialog__large-text"},__("Disconnecting the owner account will remove the Jetpack connection for all users on this site. The site will remain connected.","jetpack-connection")),_().createElement(y,{title:__("Transfer ownership to another admin","jetpack-connection"),link:(0,r.A)("calypso-settings-manage-connection",{site:window?.myJetpackInitialState?.siteSuffix}),isExternal:!0,action:"transfer"}),_().createElement(y,{title:__("View other connected accounts","jetpack-connection"),link:"users.php",action:"check-users"})),_().createElement("div",{className:"jp-connection__disconnect-dialog__actions"},_().createElement("div",{className:"jp-row"},_().createElement("div",{className:"lg-col-span-8 md-col-span-9 sm-col-span-4"},_().createElement("p",null,(0,i.createInterpolateElement)(__("<strong>Need help?</strong> Learn more about the <connectionInfoLink>Jetpack connection</connectionInfoLink> or <supportLink>contact Jetpack support</supportLink>","jetpack-connection"),{strong:_().createElement("strong",null),connectionInfoLink:_().createElement(s.ExternalLink,{href:(0,r.A)("why-the-wordpress-com-connection-is-important-for-jetpack"),className:"jp-connection__disconnect-dialog__link"}),supportLink:_().createElement(s.ExternalLink,{href:(0,r.A)("jetpack-support"),className:"jp-connection__disconnect-dialog__link"})}))),_().createElement("div",{className:"jp-connection__disconnect-dialog__button-wrap lg-col-span-4 md-col-span-7 sm-col-span-4"},_().createElement(s.Button,{variant:"primary",onClick:k,className:"jp-connection__disconnect-dialog__btn-dismiss"},__("Stay Connected","jetpack-connection")),_().createElement(s.Button,{variant:"primary",onClick:v,className:"jp-connection__disconnect-dialog__btn-disconnect",isDestructive:!0,disabled:p},p?f:b))),m&&_().createElement("p",{className:"jp-connection__disconnect-dialog__error"},m)))};f.propTypes={isOpen:g().bool,onClose:g().func,apiRoot:g().string.isRequired,apiNonce:g().string.isRequired,onDisconnected:g().func,onUnlinked:g().func};const y=({title:e,onClick:t=()=>null,isExternal:n=!1,link:o="#",action:c,disabled:r=!1})=>{const s=(0,h.useCallback)((e=>e.preventDefault()),[]);return _().createElement("div",{className:"jp-connection__manage-dialog__action-card card"+(r?" disabled":"")},_().createElement("div",{className:"jp-connection__manage-dialog__action-card__card-content"},_().createElement("a",{href:o,className:(0,u.A)("jp-connection__manage-dialog__action-card__card-headline",c),onClick:r?s:t,target:n?"_blank":"_self",rel:"noopener noreferrer"},e,_().createElement(l.A,{icon:n?d.A:p.A,className:"jp-connection__manage-dialog__action-card__icon"}))))},b=f},9660:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var o=n(5932),c=n(7999),r=n(7143),s=n(1609),i=n(4293);const a=window?.JP_CONNECTION_INITIAL_STATE||(0,c.getScriptData)()?.connection||{};function l({registrationNonce:e=a.registrationNonce,apiRoot:t=a.apiRoot,apiNonce:n=a.apiNonce,redirectUri:c,autoTrigger:l,from:d,skipUserConnection:p,skipPricingPage:u}={}){const{registerSite:m,connectUser:g,refreshConnectedPlugins:h}=(0,r.useDispatch)(i.a),_=(0,r.useSelect)((e=>e(i.a).getRegistrationError())),{siteIsRegistering:f,userIsConnecting:y,userConnectionData:b,connectedPlugins:k,connectionErrors:v,isRegistered:C,isUserConnected:E,hasConnectedOwner:j,isOfflineMode:w}=(0,r.useSelect)((e=>({siteIsRegistering:e(i.a).getSiteIsRegistering(),userIsConnecting:e(i.a).getUserIsConnecting(),userConnectionData:e(i.a).getUserConnectionData(),connectedPlugins:e(i.a).getConnectedPlugins(),connectionErrors:e(i.a).getConnectionErrors(),isOfflineMode:e(i.a).getIsOfflineMode(),...e(i.a).getConnectionStatus()}))),A=()=>p?c?(window.location=c,Promise.resolve(c)):Promise.resolve():g({from:d,redirectUri:c,skipPricingPage:u}),N=t=>(t&&t.preventDefault(),C?A():m({registrationNonce:e,redirectUri:c,from:d}).then((()=>A())));return(0,s.useEffect)((()=>{o.Ay.setApiRoot(t),o.Ay.setApiNonce(n)}),[t,n]),(0,s.useEffect)((()=>{!l||f||y||N()}),[]),{handleRegisterSite:N,handleConnectUser:A,refreshConnectedPlugins:h,isRegistered:C,isUserConnected:E,siteIsRegistering:f,userIsConnecting:y,registrationError:_,userConnectionData:b,hasConnectedOwner:j,connectedPlugins:k,connectionErrors:v,isOfflineMode:w}}},3765:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var o=n(7999);function c(){const e=("undefined"!=typeof window&&window?.JP_CONNECTION_INITIAL_STATE||(0,o.getScriptData)()?.connection)?.calypsoEnv;switch(e){case"development":return"http://calypso.localhost:3000/";case"wpcalypso":return"https://wpcalypso.wordpress.com/";case"horizon":return"https://horizon.wordpress.com/";default:return"https://wordpress.com/"}}},4617:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});const o=e=>{window.location.replace(e)}},9628:(e,t,n)=>{"use strict";n.d(t,{A:()=>s,R:()=>i});var o=n(7088),c=n(9660),r=n(1713);function s(){const{connectionErrors:e}=(0,c.A)({}),t=Object.values(e).shift(),n=t&&Object.values(t).length&&Object.values(t).shift().error_message;return{hasConnectionError:Boolean(n),connectionErrorMessage:n}}const i=()=>{const{hasConnectionError:e,connectionErrorMessage:t}=s(),{restoreConnection:n,isRestoringConnection:c,restoreConnectionError:i}=(0,r.A)();return e?React.createElement(o.A,{isRestoringConnection:c,restoreConnectionError:i,restoreConnectionCallback:n,message:t}):null}},2558:(e,t,n)=>{"use strict";n.d(t,{A:()=>y});var o=n(5932),c=n(7999),r=n(7143),s=n(4804),i=n.n(s),a=n(1609),l=n(3765),d=n(9660),p=n(4293);const u=i()("jetpack:connection:useProductCheckoutWorkflow"),{registrationNonce:m,apiRoot:g,apiNonce:h,siteSuffix:_}=window?.JP_CONNECTION_INITIAL_STATE||(0,c.getScriptData)()?.connection||{},f=()=>"undefined"!=typeof window?window?.myJetpackInitialState?.adminUrl:null;function y({productSlug:e,redirectUrl:t,siteSuffix:n=_,adminUrl:c=f(),connectAfterCheckout:s=!1,siteProductAvailabilityHandler:i=null,quantity:y=null,from:b,useBlogIdSuffix:k=!1}={}){u("productSlug is %s",e),u("redirectUrl is %s",t),u("siteSuffix is %s",n),u("from is %s",b);const[v,C]=(0,a.useState)(!1),{registerSite:E}=(0,r.useDispatch)(p.a),j=(0,r.useSelect)((e=>e(p.a).getBlogId()),[]);u("blogID is %s",j??"undefined"),k=k&&!!j;const{isUserConnected:w,isRegistered:A,handleConnectUser:N}=(0,d.A)({redirectUri:t,from:b}),S=(0,a.useMemo)((()=>{const o=(0,l.A)(),r=(!A||!w)&&s,i=r?"checkout/jetpack/":`checkout/${k?j.toString():n}/`,a=new URL(`${o}${i}${e}${null!=y?`:-q-${y}`:""}`);return r?(a.searchParams.set("connect_after_checkout",!0),a.searchParams.set("admin_url",c),a.searchParams.set("from_site_slug",n)):a.searchParams.set("site",n),a.searchParams.set("source",b),a.searchParams.set("redirect_to",t),w||a.searchParams.set("unlinked","1"),a}),[A,w,s,n,y,e,b,t,c,k,j]);u("isRegistered is %s",A),u("isUserConnected is %s",w),u("connectAfterCheckout is %s",s),u("checkoutUrl is %s",S);const R=(e=null)=>Promise.resolve(i&&i()).then((t=>{if(e&&S.searchParams.set("redirect_to",e),t)return u("handleAfterRegistration: Site has a product associated"),N();u("handleAfterRegistration: Site does not have a product associated. Redirecting to checkout %s",S),window.location.href=S}));return(0,a.useEffect)((()=>{o.Ay.setApiRoot(g),o.Ay.setApiNonce(h)}),[]),{run:(e,n=null)=>(e&&e.preventDefault(),C(!0),s?((e=null)=>{e&&S.searchParams.set("redirect_to",e),u("Redirecting to connectAfterCheckout flow: %s",S),window.location.href=S})(n):A?R(n):void E({registrationNonce:m,redirectUri:t}).then((()=>R(n)))),isRegistered:A,hasCheckoutStarted:v}}},1713:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var o=n(5932),c=n(7999),r=n(7143),s=n(1609),i=n(4293);const{apiRoot:a,apiNonce:l}=window?.JP_CONNECTION_INITIAL_STATE||(0,c.getScriptData)()?.connection||{};function d(){const[e,t]=(0,s.useState)(!1),[n,c]=(0,s.useState)(null),{disconnectUserSuccess:d,setConnectionErrors:p}=(0,r.useDispatch)(i.a);return(0,s.useEffect)((()=>{o.Ay.setApiRoot(a),o.Ay.setApiNonce(l)}),[]),{restoreConnection:(e=!0)=>(t(!0),c(null),o.Ay.reconnect().then((t=>("in_progress"===t.status?(d(),p({}),e&&(window.location.href="/wp-admin/admin.php?page=my-jetpack#/connection")):window.location.reload(),t))).catch((e=>{throw c(e),t(!1),e}))),isRestoringConnection:e,restoreConnectionError:n}}},8980:(e,t,n)=>{"use strict";n.d(t,{AY:()=>g.A,F0:()=>o.A,Hx:()=>f.a,JC:()=>l.A,Jl:()=>c.A,Ni:()=>u.A,Ob:()=>b.A,Rc:()=>d.R,Sx:()=>d.A,ag:()=>_.A,bo:()=>p.A,cS:()=>y.A,d1:()=>h.A,mX:()=>a.A,nM:()=>r.A,pK:()=>s.A,w5:()=>m.A,xW:()=>i.A});var o=n(6212),c=n(2668),r=n(7945),s=n(8421),i=n(7018),a=n(7840),l=n(7088),d=n(9628),p=n(3269),u=n(7499),m=n(9660),g=n(4981),h=n(4617),_=n(3765),f=n(4293),y=n(2558),b=n(1713)},3935:(e,t,n)=>{"use strict";n.d(t,{A1:()=>a,Ay:()=>C,DO:()=>i,Ij:()=>s,Kl:()=>m,LW:()=>l,MU:()=>g,XY:()=>d,ZO:()=>r,dz:()=>p,gH:()=>u,v_:()=>c});var o=n(5932);const c="SET_CONNECTION_STATUS",r="SET_CONNECTION_STATUS_IS_FETCHING",s="SET_SITE_IS_REGISTERING",i="SET_USER_IS_CONNECTING",a="SET_REGISTRATION_ERROR",l="CLEAR_REGISTRATION_ERROR",d="SET_AUTHORIZATION_URL",p="DISCONNECT_USER_SUCCESS",u="SET_CONNECTED_PLUGINS",m="SET_CONNECTION_ERRORS",g="SET_IS_OFFLINE_MODE",h=e=>({type:c,connectionStatus:e}),_=e=>({type:s,isRegistering:e}),f=e=>({type:i,isConnecting:e}),y=e=>({type:a,registrationError:e}),b=()=>({type:l}),k=e=>({type:d,authorizationUrl:e}),v=e=>({type:u,connectedPlugins:e});const C={setConnectionStatus:h,setConnectionStatusIsFetching:e=>({type:r,isFetching:e}),fetchConnectionStatus:()=>({type:"FETCH_CONNECTION_STATUS"}),fetchAuthorizationUrl:e=>({type:"FETCH_AUTHORIZATION_URL",redirectUri:e}),setSiteIsRegistering:_,setUserIsConnecting:f,setRegistrationError:y,clearRegistrationError:b,setAuthorizationUrl:k,registerSite:function*({registrationNonce:e,redirectUri:t,from:n=""}){yield b(),yield _(!0);try{const o=yield{type:"REGISTER_SITE",registrationNonce:e,redirectUri:t,from:n};return yield h({isRegistered:!0}),yield k(o.authorizeUrl),yield _(!1),Promise.resolve(o)}catch(e){return yield y(e),yield _(!1),Promise.reject(e)}},connectUser:function*({from:e,redirectFunc:t,redirectUri:n,skipPricingPage:o}={}){yield f(!0),yield{type:"CONNECT_USER",from:e,redirectFunc:t,redirectUri:n,skipPricingPage:o}},disconnectUserSuccess:()=>({type:p}),setConnectedPlugins:v,refreshConnectedPlugins:()=>async({dispatch:e})=>await new Promise((t=>o.Ay.fetchConnectedPlugins().then((n=>{e(v(n)),t(n)})))),setConnectionErrors:e=>({type:m,connectionErrors:e}),setIsOfflineMode:e=>({type:g,isOfflineMode:e})}},2494:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var o=n(5932),c=n(7143),r=n(2279);const s={FETCH_AUTHORIZATION_URL:({redirectUri:e})=>o.Ay.fetchAuthorizationUrl(e),REGISTER_SITE:({redirectUri:e,from:t})=>o.Ay.registerSite(null,e,t),CONNECT_USER:(0,c.createRegistryControl)((({resolveSelect:e})=>({from:t,redirectFunc:n,redirectUri:o,skipPricingPage:c}={})=>new Promise(((s,i)=>{e(r.A).getAuthorizationUrl(o).then((e=>{const o=n||(e=>window.location.assign(e)),r=new URL(e);c&&r.searchParams.set("skip_pricing","true"),t&&r.searchParams.set("from",encodeURIComponent(t));const i=r.toString();o(i),s(i)})).catch((e=>{i(e)}))}))))}},5051:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var o=n(7143),c=n(3935);const r=(0,o.combineReducers)({connectionStatus:(e={},t)=>{switch(t.type){case c.v_:return{...e,...t.connectionStatus};case c.dz:return{...e,isUserConnected:!1}}return e},connectionStatusIsFetching:(e=!1,t)=>t.type===c.ZO?t.isFetching:e,siteIsRegistering:(e=!1,t)=>t.type===c.Ij?t.isRegistering:e,userIsConnecting:(e=!1,t)=>t.type===c.DO?t.isConnecting:e,registrationError:(e,t)=>{switch(t.type){case c.LW:return!1;case c.A1:return t.registrationError;default:return e}},authorizationUrl:(e,t)=>t.type===c.XY?t.authorizationUrl:e,userConnectionData:(e,t)=>(t.type,e),connectedPlugins:(e={},t)=>t.type===c.gH?t.connectedPlugins:e,connectionErrors:(e={},t)=>t.type===c.Kl?t.connectionErrors:e,isOfflineMode:(e=!1,t)=>t.type===c.MU?t.isConnecting:e})},8019:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var o=n(7143),c=n(3935),r=n(2279);const s={...{getAuthorizationUrl:{isFulfilled:(e,...t)=>{const n=Boolean(e.authorizationUrl),c=(0,o.select)(r.A).hasFinishedResolution("getAuthorizationUrl",t);return n&&!c&&(0,o.dispatch)(r.A).finishResolution("getAuthorizationUrl",t),n},*fulfill(e){const t=yield c.Ay.fetchAuthorizationUrl(e);yield c.Ay.setAuthorizationUrl(t.authorizeUrl)}}}}},2676:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});const o={...{getConnectionStatus:e=>e.connectionStatus||{},getConnectionStatusIsFetching:()=>!1,getSiteIsRegistering:e=>e.siteIsRegistering||!1,getUserIsConnecting:e=>e.userIsConnecting||!1,getRegistrationError:e=>e.registrationError||!1,getAuthorizationUrl:e=>e.authorizationUrl||!1,getUserConnectionData:e=>e.userConnectionData||!1,getConnectedPlugins:e=>e.connectedPlugins||[],getConnectionErrors:e=>e.connectionErrors||[],getIsOfflineMode:e=>e.isOfflineMode||!1,getWpcomUser:e=>e?.userConnectionData?.currentUser?.wpcomUser,getBlogId:e=>e?.userConnectionData?.currentUser?.blogId}}},8734:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var o=n(7143);class c{static store=null;static mayBeInit(e,t){null===c.store&&(c.store=(0,o.createReduxStore)(e,t),(0,o.register)(c.store))}}const r=c},2279:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});const o="jetpack-connection"},4293:(e,t,n)=>{"use strict";n.d(t,{a:()=>d.A});var o=n(7999),c=n(3935),r=n(2494),s=n(5051),i=n(8019),a=n(2676),l=n(8734),d=n(2279);const p=window.JP_CONNECTION_INITIAL_STATE||(0,o.getScriptData)()?.connection;p||console.error("Jetpack Connection package: Initial state is missing. Check documentation to see how to use the Connection composer package to set up the initial state."),l.A.mayBeInit(d.A,{__experimentalUseThunks:!0,reducer:s.A,actions:c.Ay,selectors:a.A,resolvers:i.A,controls:r.A,initialState:p||{}})},3673:(e,t,n)=>{"use strict";n.d(t,{$:()=>c,M:()=>o});const o="en",c="USD"},1452:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var o=n(8443),c=n(3673),r=n(9980),s=n(1167);const i=function(){let e,t;const n=()=>{const{l10n:{locale:t}}=(0,o.getSettings)();return(e??(t||window?.window?.navigator?.language)??c.M).split("_")[0]};return{setLocale:t=>{e=t},setGeoLocation:e=>{t=e},formatNumber:(e,{decimals:t=0,forceLatin:o=!0,numberFormatOptions:c={}}={})=>{try{return(0,s.j)({browserSafeLocale:n(),decimals:t,forceLatin:o,numberFormatOptions:c}).format(e)}catch{return String(e)}},formatNumberCompact:(e,{decimals:t=0,forceLatin:o=!0,numberFormatOptions:c={}}={})=>{try{return(0,s.c)({browserSafeLocale:n(),decimals:t,forceLatin:o,numberFormatOptions:c}).format(e)}catch{return String(e)}},formatCurrency:(e,o,{stripZeros:c=!1,isSmallestUnit:s=!1,signForPositive:i=!1,forceLatin:a=!0}={})=>(0,r.u)({number:e,currency:o,browserSafeLocale:n(),stripZeros:c,isSmallestUnit:s,signForPositive:i,geoLocation:t,forceLatin:a}),getCurrencyObject:(e,o,{stripZeros:c=!1,isSmallestUnit:s=!1,signForPositive:i=!1,forceLatin:a=!0}={})=>(0,r.v)({number:e,currency:o,browserSafeLocale:n(),stripZeros:c,isSmallestUnit:s,signForPositive:i,geoLocation:t,forceLatin:a})}}},3328:(e,t,n)=>{"use strict";n.d(t,{J:()=>a});var o=n(4804),c=n.n(o),r=n(3673);const s=c()("number-formatters:get-cached-formatter"),i=new Map;function a({locale:e,fallbackLocale:t=r.M,options:n,retries:o=1}){const c=JSON.stringify([e,n]);try{return i.get(c)??i.set(c,new Intl.NumberFormat(e,n)).get(c)}catch(c){if(s(`Intl.NumberFormat was called with a non-existent locale "${e}"; falling back to ${t}`),o)return a({locale:t,options:n,retries:o-1});throw c}}},4268:(e,t,n)=>{"use strict";n.d(t,{vA:()=>l});const o=(0,n(1452).A)(),{setLocale:c,setGeoLocation:r,formatNumber:s,formatNumberCompact:i,formatCurrency:a,getCurrencyObject:l}=o},6673:(e,t,n)=>{"use strict";n.d(t,{a:()=>o});const o={AED:{symbol:"د.إ.‏"},AFN:{symbol:"؋"},ALL:{symbol:"Lek"},AMD:{symbol:"֏"},ANG:{symbol:"ƒ"},AOA:{symbol:"Kz"},ARS:{symbol:"$"},AUD:{symbol:"A$"},AWG:{symbol:"ƒ"},AZN:{symbol:"₼"},BAM:{symbol:"КМ"},BBD:{symbol:"Bds$"},BDT:{symbol:"৳"},BGN:{symbol:"лв."},BHD:{symbol:"د.ب.‏"},BIF:{symbol:"FBu"},BMD:{symbol:"$"},BND:{symbol:"$"},BOB:{symbol:"Bs"},BRL:{symbol:"R$"},BSD:{symbol:"$"},BTC:{symbol:"Ƀ"},BTN:{symbol:"Nu."},BWP:{symbol:"P"},BYR:{symbol:"р."},BZD:{symbol:"BZ$"},CAD:{symbol:"C$"},CDF:{symbol:"FC"},CHF:{symbol:"CHF"},CLP:{symbol:"$"},CNY:{symbol:"¥"},COP:{symbol:"$"},CRC:{symbol:"₡"},CUC:{symbol:"CUC"},CUP:{symbol:"$MN"},CVE:{symbol:"$"},CZK:{symbol:"Kč"},DJF:{symbol:"Fdj"},DKK:{symbol:"kr."},DOP:{symbol:"RD$"},DZD:{symbol:"د.ج.‏"},EGP:{symbol:"ج.م.‏"},ERN:{symbol:"Nfk"},ETB:{symbol:"ETB"},EUR:{symbol:"€"},FJD:{symbol:"FJ$"},FKP:{symbol:"£"},GBP:{symbol:"£"},GEL:{symbol:"Lari"},GHS:{symbol:"₵"},GIP:{symbol:"£"},GMD:{symbol:"D"},GNF:{symbol:"FG"},GTQ:{symbol:"Q"},GYD:{symbol:"G$"},HKD:{symbol:"HK$"},HNL:{symbol:"L."},HRK:{symbol:"kn"},HTG:{symbol:"G"},HUF:{symbol:"Ft"},IDR:{symbol:"Rp"},ILS:{symbol:"₪"},INR:{symbol:"₹"},IQD:{symbol:"د.ع.‏"},IRR:{symbol:"﷼"},ISK:{symbol:"kr."},JMD:{symbol:"J$"},JOD:{symbol:"د.ا.‏"},JPY:{symbol:"¥"},KES:{symbol:"S"},KGS:{symbol:"сом"},KHR:{symbol:"៛"},KMF:{symbol:"CF"},KPW:{symbol:"₩"},KRW:{symbol:"₩"},KWD:{symbol:"د.ك.‏"},KYD:{symbol:"$"},KZT:{symbol:"₸"},LAK:{symbol:"₭"},LBP:{symbol:"ل.ل.‏"},LKR:{symbol:"₨"},LRD:{symbol:"L$"},LSL:{symbol:"M"},LYD:{symbol:"د.ل.‏"},MAD:{symbol:"د.م.‏"},MDL:{symbol:"lei"},MGA:{symbol:"Ar"},MKD:{symbol:"ден."},MMK:{symbol:"K"},MNT:{symbol:"₮"},MOP:{symbol:"MOP$"},MRO:{symbol:"UM"},MTL:{symbol:"₤"},MUR:{symbol:"₨"},MVR:{symbol:"MVR"},MWK:{symbol:"MK"},MXN:{symbol:"MX$"},MYR:{symbol:"RM"},MZN:{symbol:"MT"},NAD:{symbol:"N$"},NGN:{symbol:"₦"},NIO:{symbol:"C$"},NOK:{symbol:"kr"},NPR:{symbol:"₨"},NZD:{symbol:"NZ$"},OMR:{symbol:"﷼"},PAB:{symbol:"B/."},PEN:{symbol:"S/."},PGK:{symbol:"K"},PHP:{symbol:"₱"},PKR:{symbol:"₨"},PLN:{symbol:"zł"},PYG:{symbol:"₲"},QAR:{symbol:"﷼"},RON:{symbol:"lei"},RSD:{symbol:"Дин."},RUB:{symbol:"₽"},RWF:{symbol:"RWF"},SAR:{symbol:"﷼"},SBD:{symbol:"S$"},SCR:{symbol:"₨"},SDD:{symbol:"LSd"},SDG:{symbol:"£‏"},SEK:{symbol:"kr"},SGD:{symbol:"S$"},SHP:{symbol:"£"},SLL:{symbol:"Le"},SOS:{symbol:"S"},SRD:{symbol:"$"},STD:{symbol:"Db"},SVC:{symbol:"₡"},SYP:{symbol:"£"},SZL:{symbol:"E"},THB:{symbol:"฿"},TJS:{symbol:"TJS"},TMT:{symbol:"m"},TND:{symbol:"د.ت.‏"},TOP:{symbol:"T$"},TRY:{symbol:"TL"},TTD:{symbol:"TT$"},TVD:{symbol:"$T"},TWD:{symbol:"NT$"},TZS:{symbol:"TSh"},UAH:{symbol:"₴"},UGX:{symbol:"USh"},USD:{},UYU:{symbol:"$U"},UZS:{symbol:"сўм"},VEB:{symbol:"Bs."},VEF:{symbol:"Bs. F."},VND:{symbol:"₫"},VUV:{symbol:"VT"},WST:{symbol:"WS$"},XAF:{symbol:"F"},XCD:{symbol:"$"},XOF:{symbol:"F"},XPF:{symbol:"F"},YER:{symbol:"﷼"},ZAR:{symbol:"R"},ZMW:{symbol:"ZK"},WON:{symbol:"₩"}}},9980:(e,t,n)=>{"use strict";n.d(t,{u:()=>h,v:()=>_});var o=n(4804),c=n.n(o),r=n(3673),s=n(3328),i=n(6673);const a=c()("number-formatters:number-format-currency");function l(e,t){return"USD"===e&&t&&""!==t&&"US"!==t?{symbol:"US$"}:i.a[e]}function d(e,t){return l(e,t)?e:(a(`getValidCurrency was called with a non-existent currency "${e}"; falling back to ${r.$}`),r.$)}function p({number:e,currency:t,browserSafeLocale:n,forceLatin:o=!0,stripZeros:c,signForPositive:r}){const i=`${n}${o?"-u-nu-latn":""}`,a={style:"currency",currency:t,...c&&Number.isInteger(e)&&{maximumFractionDigits:0,minimumFractionDigits:0},...r&&{signDisplay:"exceptZero"}};return(0,s.J)({locale:i,options:a})}function u(e,t,n){return p({number:0,currency:t,browserSafeLocale:e,forceLatin:n}).resolvedOptions().maximumFractionDigits}function m(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}function g(e,t,n){if(isNaN(e))return a("formatCurrency was called with NaN"),0;if(n){Number.isInteger(e)||a("formatCurrency was called with isSmallestUnit and a float which will be rounded",e);const n=10**t;return m(Math.round(e)/n,t)}return m(e,t)}const h=({number:e,browserSafeLocale:t,currency:n,stripZeros:o,isSmallestUnit:c,signForPositive:r,geoLocation:s,forceLatin:i})=>{const a=d(n,s),m=l(a,s),h=u(t,a,i);if(c&&void 0===h)throw new Error(`Could not determine currency precision for ${a} in ${t}`);const _=g(e,h??0,c);return p({number:_,currency:a,browserSafeLocale:t,forceLatin:i,stripZeros:o,signForPositive:r}).formatToParts(_).reduce(((e,t)=>"currency"===t.type&&m?.symbol?e+m.symbol:e+t.value),"")},_=({number:e,browserSafeLocale:t,currency:n,stripZeros:o,isSmallestUnit:c,signForPositive:r,geoLocation:s,forceLatin:i})=>{const a=d(n,s),m=l(a,s),h=g(e,u(t,a,i)??0,c),_=p({number:h,currency:a,browserSafeLocale:t,forceLatin:i,stripZeros:o,signForPositive:r}).formatToParts(h);let f="",y="$",b="before",k=!1,v=!1,C="",E="";_.forEach((e=>{switch(e.type){case"currency":return y=m?.symbol??e.value,void(k&&(b="after"));case"group":case"integer":return C+=e.value,void(k=!0);case"decimal":case"fraction":return E+=e.value,k=!0,void(v=!0);case"minusSign":return void(f="-");case"plusSign":f="+"}}));const j=!Number.isInteger(h)&&v;return{sign:f,symbol:y,symbolPosition:b,integer:C,fraction:E,hasNonZeroFraction:j}}},1167:(e,t,n)=>{"use strict";n.d(t,{c:()=>r,j:()=>c});var o=n(3328);const c=({browserSafeLocale:e,decimals:t=0,forceLatin:n=!0,numberFormatOptions:c={}})=>{const r=`${e}${n?"-u-nu-latn":""}`,s={minimumFractionDigits:t,maximumFractionDigits:t,...c};return(0,o.J)({locale:r,options:s})},r=({numberFormatOptions:e={},...t})=>c({...t,numberFormatOptions:{notation:"compact",maximumFractionDigits:1,...e}})},2365:(e,t,n)=>{"use strict";e.exports=n.p+"images/disconnect-confirm-dc9fe8f5c68cfd1320e0.jpg"},9362:(e,t,n)=>{"use strict";e.exports=n.p+"images/disconnect-thanks-5873bfac56a9bd7322cd.jpg"},9074:e=>{"use strict";e.exports={consumer_slug:"connection_package"}},7999:e=>{"use strict";e.exports=window.JetpackScriptDataModule},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},6427:e=>{"use strict";e.exports=window.wp.components},9491:e=>{"use strict";e.exports=window.wp.compose},7143:e=>{"use strict";e.exports=window.wp.data},8443:e=>{"use strict";e.exports=window.wp.date},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},5573:e=>{"use strict";e.exports=window.wp.primitives},3832:e=>{"use strict";e.exports=window.wp.url},8579:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},2231:(e,t,n)=>{"use strict";function o(e){var t,n,c="";if("string"==typeof e||"number"==typeof e)c+=e;else if("object"==typeof e)if(Array.isArray(e)){var r=e.length;for(t=0;t<r;t++)e[t]&&(n=o(e[t]))&&(c&&(c+=" "),c+=n)}else for(n in e)e[n]&&(c&&(c+=" "),c+=n);return c}n.d(t,{A:()=>c});const c=function(){for(var e,t,n=0,c="",r=arguments.length;n<r;n++)(e=arguments[n])&&(t=o(e))&&(c&&(c+=" "),c+=t);return c}}},t={};function n(o){var c=t[o];if(void 0!==c)return c.exports;var r=t[o]={exports:{}};return e[o](r,r.exports,n),r.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;n.g.importScripts&&(e=n.g.location+"");var t=n.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var o=t.getElementsByTagName("script");if(o.length)for(var c=o.length-1;c>-1&&(!e||!/^http(s?):/.test(e));)e=o[c--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),n.p=e})();var o={};return(()=>{"use strict";n.r(o),n.d(o,{CONNECTION_STORE_ID:()=>e.Hx,ConnectButton:()=>e.pK,ConnectScreen:()=>e.F0,ConnectScreenLayout:()=>e.Jl,ConnectScreenRequiredPlan:()=>e.nM,ConnectUser:()=>e.mX,ConnectionError:()=>e.Rc,ConnectionErrorNotice:()=>e.JC,DisconnectCard:()=>e.Ni,DisconnectDialog:()=>e.bo,InPlaceConnection:()=>e.xW,ManageConnectionDialog:()=>e.AY,getCalypsoOrigin:()=>e.ag,thirdPartyCookiesFallbackHelper:()=>e.d1,useConnection:()=>e.w5,useConnectionErrorNotice:()=>e.Sx,useProductCheckoutWorkflow:()=>e.cS,useRestoreConnection:()=>e.Ob});var e=n(8980)})(),o})()));